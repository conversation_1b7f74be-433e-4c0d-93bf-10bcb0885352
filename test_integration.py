#!/usr/bin/env python3
"""
Integration test for Mandy AI Companion with real Graphiti integration.

This test verifies that the complete system works end-to-end:
1. Neo4j database is running
2. Graphiti server is operational
3. Core Chat Engine can process messages
4. Memory is being stored and retrieved
5. Emotional intelligence is working
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from uuid import uuid4

import httpx


class MandyIntegrationTester:
    """Comprehensive integration tester for Mandy AI Companion."""
    
    def __init__(self):
        """Initialize the integration tester."""
        self.core_api_url = "http://localhost:8000"
        self.neo4j_url = "http://localhost:7474"
        self.access_token = None
        self.conversation_id = None
        self.test_user_id = None
    
    async def test_infrastructure(self):
        """Test that all infrastructure components are running."""
        print("🏗️  Testing Infrastructure Components...")
        
        components = [
            ("Neo4j Database", self.neo4j_url),
            ("Core Chat Engine", f"{self.core_api_url}/health"),
        ]
        
        all_healthy = True
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            for name, url in components:
                try:
                    response = await client.get(url)
                    if response.status_code == 200:
                        print(f"✅ {name}: Healthy")
                    else:
                        print(f"❌ {name}: Unhealthy (status: {response.status_code})")
                        all_healthy = False
                except Exception as e:
                    print(f"❌ {name}: Failed to connect ({str(e)})")
                    all_healthy = False
        
        return all_healthy
    
    async def test_authentication(self):
        """Test user authentication."""
        print("\n🔐 Testing Authentication...")
        
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    f"{self.core_api_url}/auth/login",
                    json={
                        "username": "admin",
                        "password": "admin123"
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        self.access_token = data["data"]["access_token"]
                        self.test_user_id = data["data"]["user"]["id"]
                        print(f"✅ Authentication successful")
                        return True
                    else:
                        print(f"❌ Authentication failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Authentication failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Authentication error: {e}")
                return False
    

    
    async def test_conversation_flow(self):
        """Test the complete conversation flow."""
        print("\n💬 Testing Complete Conversation Flow...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        test_messages = [
            {
                "message": "Hello Mandy! I'm feeling really excited about starting a new project today!",
                "expected_emotions": ["joy", "anticipation"],
                "description": "Happy message"
            },
            {
                "message": "Actually, I'm a bit worried about whether I'll be able to finish it on time.",
                "expected_emotions": ["fear", "anxiety"],
                "description": "Worried follow-up"
            },
            {
                "message": "Do you remember what I just told you about my project?",
                "expected_memory": True,
                "description": "Memory test"
            }
        ]
        
        conversation_id = str(uuid4())
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            for i, test_case in enumerate(test_messages, 1):
                print(f"\n  📝 Test Message {i}: {test_case['description']}")
                
                try:
                    response = await client.post(
                        f"{self.core_api_url}/chat/send",
                        headers={"Authorization": f"Bearer {self.access_token}"},
                        json={
                            "message": test_case["message"],
                            "conversation_id": conversation_id,
                            "context": {"test": True, "test_case": i}
                        }
                    )
                    
                    if response.status_code == 200:
                        data = response.json()
                        if data["success"]:
                            chat_data = data["data"]
                            
                            # Check emotions
                            if "expected_emotions" in test_case:
                                emotions = [e["type"] for e in chat_data["user_emotions"]]
                                expected = test_case["expected_emotions"]
                                emotion_match = any(exp in emotions for exp in expected)
                                
                                if emotion_match:
                                    print(f"    ✅ Emotions detected: {emotions}")
                                else:
                                    print(f"    ⚠️  Expected emotions {expected}, got {emotions}")
                            
                            # Check memory usage
                            if test_case.get("expected_memory"):
                                context_used = chat_data.get("context_used", {})
                                memories = context_used.get("retrieved_memories", 0)
                                
                                if memories > 0:
                                    print(f"    ✅ Memory retrieval: {memories} memories used")
                                else:
                                    print(f"    ⚠️  Expected memory usage, but no memories retrieved")
                            
                            # Check response
                            messages = chat_data["messages"]
                            print(f"    ✅ Response: {len(messages)} message(s)")
                            
                            # Show first message content (truncated)
                            if messages:
                                first_msg = messages[0]["content"]
                                preview = first_msg[:100] + "..." if len(first_msg) > 100 else first_msg
                                print(f"    💭 Mandy: {preview}")
                        else:
                            print(f"    ❌ Chat failed: {data['message']}")
                            return False
                    else:
                        print(f"    ❌ Chat request failed: {response.status_code}")
                        return False
                        
                except Exception as e:
                    print(f"    ❌ Chat error: {e}")
                    return False
                
                # Small delay between messages
                await asyncio.sleep(1)
        
        print("\n✅ Conversation flow test completed successfully!")
        return True
    
    async def test_memory_persistence(self):
        """Test that memories persist across conversations."""
        print("\n🧠 Testing Memory Persistence...")
        
        if not self.access_token:
            print("❌ No access token available")
            return False
        
        # Send a message with specific information
        memorable_info = f"My favorite color is purple and I love hiking. Test ID: {uuid4().hex[:8]}"
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            try:
                # First conversation
                response = await client.post(
                    f"{self.core_api_url}/chat/send",
                    headers={"Authorization": f"Bearer {self.access_token}"},
                    json={
                        "message": memorable_info,
                        "conversation_id": str(uuid4()),
                        "context": {"memory_test": True}
                    }
                )
                
                if response.status_code != 200:
                    print(f"❌ Failed to send memorable message: {response.status_code}")
                    return False
                
                print("✅ Sent memorable information")
                
                # Wait a moment for processing
                await asyncio.sleep(2)
                
                # Second conversation - ask about the information
                response = await client.post(
                    f"{self.core_api_url}/chat/send",
                    headers={"Authorization": f"Bearer {self.access_token}"},
                    json={
                        "message": "What's my favorite color?",
                        "conversation_id": str(uuid4()),  # Different conversation
                        "context": {"memory_test": True}
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data["success"]:
                        context_used = data["data"].get("context_used", {})
                        memories_retrieved = context_used.get("retrieved_memories", 0)
                        
                        if memories_retrieved > 0:
                            print(f"✅ Memory persistence: {memories_retrieved} memories retrieved")
                            return True
                        else:
                            print("⚠️  No memories retrieved - memory persistence may not be working")
                            return False
                    else:
                        print(f"❌ Memory test failed: {data['message']}")
                        return False
                else:
                    print(f"❌ Memory test request failed: {response.status_code}")
                    return False
                    
            except Exception as e:
                print(f"❌ Memory persistence test error: {e}")
                return False
    
    async def run_all_tests(self):
        """Run all integration tests."""
        print("🤖 MANDY AI COMPANION - INTEGRATION TESTS")
        print("=" * 60)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        tests = [
            ("Infrastructure Health", self.test_infrastructure()),
            ("Authentication", self.test_authentication()),
            ("Conversation Flow", self.test_conversation_flow()),
            ("Memory Persistence", self.test_memory_persistence()),
        ]
        
        results = []
        
        for test_name, test_coro in tests:
            print(f"\n🧪 Running: {test_name}")
            try:
                result = await test_coro
                results.append((test_name, result))
                
                if result:
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
                    
            except Exception as e:
                print(f"💥 {test_name}: CRASHED - {e}")
                results.append((test_name, False))
        
        # Print final summary
        print("\n" + "=" * 60)
        print("📊 INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\n📈 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Mandy's brain is fully operational!")
            print("🧠 Memory system is working")
            print("💭 Emotional intelligence is active")
            print("🔗 All services are connected")
            print("\n🚀 Mandy is ready for authentic human connection!")
        else:
            print(f"\n⚠️  {total - passed} test(s) failed. Check the logs above.")
            print("🔧 Please fix the issues before proceeding.")
        
        return passed == total


async def main():
    """Main function to run integration tests."""
    tester = MandyIntegrationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n🎯 Integration tests completed successfully!")
        sys.exit(0)
    else:
        print("\n🔧 Some tests failed. Please check the system.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
