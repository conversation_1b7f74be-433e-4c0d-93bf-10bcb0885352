"""
Content Fetcher Worker for Mandy AI Companion

This worker service periodically fetches and processes external content
to enrich <PERSON>'s knowledge base and conversation capabilities.
"""

import asyncio
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional

import httpx
from loguru import logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class ContentFetcher:
    """Worker service for fetching and processing external content."""

    def __init__(self):
        """Initialize the content fetcher."""
        self.core_api_url = os.getenv("CORE_CHAT_ENGINE_URL", "http://core-chat-engine:8000")
        self.scheduler_interval = int(os.getenv("SCHEDULER_INTERVAL", "3600"))  # 1 hour default
        self.client = httpx.AsyncClient(timeout=30.0)
        self.running = False

        logger.info("Content Fetcher initialized")

    async def start(self):
        """Start the content fetcher worker."""
        self.running = True
        logger.info("Starting Content Fetcher worker...")

        while self.running:
            try:
                await self.fetch_and_process_content()

                # Wait for next cycle
                logger.info(f"Content fetch cycle completed. Sleeping for {self.scheduler_interval} seconds.")
                await asyncio.sleep(self.scheduler_interval)

            except Exception as e:
                logger.error(f"Error in content fetcher cycle: {str(e)}", exc_info=True)
                # Sleep for a shorter time on error
                await asyncio.sleep(60)

    async def fetch_and_process_content(self):
        """Fetch and process external content."""
        logger.info("Starting content fetch and processing cycle")

        # Example content sources - in production, these would be real APIs
        content_sources = [
            {
                "name": "daily_inspiration",
                "description": "Daily inspirational quotes and thoughts",
                "content": await self.generate_daily_inspiration()
            },
            {
                "name": "conversation_starters",
                "description": "Interesting conversation topics",
                "content": await self.generate_conversation_starters()
            },
            {
                "name": "emotional_intelligence_tips",
                "description": "Tips for emotional intelligence and empathy",
                "content": await self.generate_emotional_tips()
            }
        ]

        # Process each content source
        for source in content_sources:
            try:
                await self.process_content_source(source)
            except Exception as e:
                logger.error(f"Error processing content source {source['name']}: {str(e)}")

    async def generate_daily_inspiration(self) -> str:
        """Generate daily inspirational content."""
        inspirations = [
            "Every conversation is an opportunity to learn something new about someone.",
            "Listening with empathy can transform a simple chat into a meaningful connection.",
            "The best conversations happen when both people feel truly heard and understood.",
            "Small acts of kindness in conversation can brighten someone's entire day.",
            "Being genuinely curious about others creates the foundation for deep friendships."
        ]

        # Rotate based on day of year
        day_of_year = datetime.now().timetuple().tm_yday
        return inspirations[day_of_year % len(inspirations)]

    async def generate_conversation_starters(self) -> str:
        """Generate interesting conversation starters."""
        starters = [
            "What's something you've learned recently that changed how you think about the world?",
            "If you could have dinner with anyone, living or dead, who would it be and why?",
            "What's a small thing that happened today that made you smile?",
            "What's something you're looking forward to in the near future?",
            "What's a skill you'd love to learn if you had unlimited time?",
            "What's the best advice you've ever received?",
            "What's something that always makes you feel better when you're having a tough day?"
        ]

        # Rotate based on day of week
        day_of_week = datetime.now().weekday()
        return starters[day_of_week % len(starters)]

    async def generate_emotional_tips(self) -> str:
        """Generate emotional intelligence tips."""
        tips = [
            "When someone shares something difficult, sometimes the best response is simply 'That sounds really hard.'",
            "Asking 'How did that make you feel?' shows you care about someone's emotional experience.",
            "Reflecting back what you heard ('It sounds like you're feeling...') helps people feel understood.",
            "It's okay to sit with someone in their difficult emotions rather than trying to fix everything.",
            "Celebrating small wins with genuine enthusiasm can mean the world to someone.",
            "Sometimes the most supportive thing you can say is 'I'm here for you, whatever you need.'"
        ]

        # Rotate based on hour of day
        hour = datetime.now().hour
        return tips[hour % len(tips)]

    async def process_content_source(self, source: Dict):
        """Process a content source and send to core API."""
        try:
            # Create episode data for the content
            episode_data = {
                "source": source["name"],
                "description": source["description"],
                "content": source["content"],
                "timestamp": datetime.utcnow().isoformat(),
                "type": "background_content",
                "processed_by": "content_fetcher"
            }

            logger.info(f"Processing content source: {source['name']} - {source['content'][:100]}...")

        except Exception as e:
            logger.error(f"Error processing content source {source['name']}: {str(e)}")

    async def stop(self):
        """Stop the content fetcher worker."""
        self.running = False
        await self.client.aclose()
        logger.info("Content Fetcher stopped")


async def main():
    """Main function to run the content fetcher."""
    logger.info("Starting Content Fetcher Worker...")

    fetcher = ContentFetcher()

    try:
        await fetcher.start()
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
    finally:
        await fetcher.stop()


if __name__ == "__main__":
    asyncio.run(main())
