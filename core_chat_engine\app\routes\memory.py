"""
Memory management routes for Graphiti knowledge graph operations.

This module provides endpoints for managing <PERSON>'s memory system,
including adding episodes, searching memories, and managing knowledge graph data.
"""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from pydantic import BaseModel, Field

from shared.models.base import User, Episode, SearchQuery, SearchResult, APIResponse
from shared.utils.logging import get_logger

from app.routes.auth import get_current_user
from app.services.graphiti_service import GraphitiService, get_graphiti_service


# Initialize router
router = APIRouter()

# Logger
logger = get_logger("memory")


# Request/Response models
class AddEpisodeRequest(BaseModel):
    """Request to add a new episode to memory."""
    content: str = Field(..., min_length=1, max_length=10000)
    episode_type: str = Field(default="manual", max_length=50)
    source: str = Field(default="api", max_length=50)
    metadata: Optional[Dict] = Field(default_factory=dict)


class MemorySearchRequest(BaseModel):
    """Request to search memory."""
    query: str = Field(..., min_length=1, max_length=500)
    search_type: str = Field(default="hybrid", regex="^(semantic|keyword|graph|hybrid)$")
    limit: int = Field(default=10, ge=1, le=50)
    filters: Optional[Dict] = Field(default_factory=dict)


class MemoryStats(BaseModel):
    """Memory statistics."""
    total_episodes: int
    total_nodes: int
    total_edges: int
    memory_size_mb: float
    last_updated: datetime


@router.post(
    "/episodes",
    response_model=APIResponse,
    summary="Add memory episode",
    description="Add a new episode to Mandy's memory system",
)
async def add_episode(
    request: AddEpisodeRequest,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Add a new episode to Mandy's memory system.
    
    This endpoint allows manual addition of episodes to the knowledge graph,
    which can be useful for importing external data or adding context.
    
    Args:
        request: Episode data to add
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Episode addition confirmation
    """
    try:
        # Create episode object
        episode = Episode(
            user_id=current_user.id,
            group_id=current_user.group_id,
            content=request.content,
            episode_type=request.episode_type,
            source=request.source,
            metadata=request.metadata or {}
        )
        
        # Add episode to Graphiti
        episode_id = await graphiti_service.add_episode(episode)
        
        logger.info(
            "Added memory episode",
            user_id=str(current_user.id),
            episode_id=str(episode_id),
            episode_type=request.episode_type,
            content_length=len(request.content)
        )
        
        return APIResponse(
            success=True,
            message="Episode added successfully",
            data={
                "episode_id": str(episode_id),
                "episode_type": request.episode_type,
                "content_length": len(request.content)
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to add episode: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add episode"
        )


@router.post(
    "/search",
    response_model=APIResponse,
    summary="Search memory",
    description="Search through Mandy's memory using various search methods",
)
async def search_memory(
    request: MemorySearchRequest,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Search through Mandy's memory system.
    
    This endpoint provides access to Graphiti's powerful search capabilities,
    including semantic search, keyword search, graph traversal, and hybrid approaches.
    
    Args:
        request: Search parameters
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Search results
    """
    try:
        # Create search query
        search_query = SearchQuery(
            query=request.query,
            user_id=current_user.id,
            group_id=current_user.group_id,
            limit=request.limit,
            filters=request.filters,
            search_type=request.search_type
        )
        
        # Perform search
        results = await graphiti_service.search_memory(search_query)
        
        logger.info(
            "Performed memory search",
            user_id=str(current_user.id),
            query=request.query,
            search_type=request.search_type,
            results_count=len(results),
            limit=request.limit
        )
        
        return APIResponse(
            success=True,
            message="Search completed successfully",
            data={
                "results": [result.dict() for result in results],
                "query": request.query,
                "search_type": request.search_type,
                "total_results": len(results)
            }
        )
        
    except Exception as e:
        logger.error(f"Memory search failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Search failed"
        )


@router.get(
    "/stats",
    response_model=APIResponse,
    summary="Get memory statistics",
    description="Get statistics about the user's memory graph",
)
async def get_memory_stats(
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Get statistics about the user's memory graph.
    
    Args:
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Memory statistics
    """
    try:
        # Get memory statistics from Graphiti
        stats = await graphiti_service.get_memory_statistics(
            user_id=current_user.id,
            group_id=current_user.group_id
        )
        
        memory_stats = MemoryStats(
            total_episodes=stats.get("total_episodes", 0),
            total_nodes=stats.get("total_nodes", 0),
            total_edges=stats.get("total_edges", 0),
            memory_size_mb=stats.get("memory_size_mb", 0.0),
            last_updated=stats.get("last_updated", datetime.utcnow())
        )
        
        logger.info(
            "Retrieved memory statistics",
            user_id=str(current_user.id),
            total_episodes=memory_stats.total_episodes,
            total_nodes=memory_stats.total_nodes
        )
        
        return APIResponse(
            success=True,
            message="Memory statistics retrieved successfully",
            data=memory_stats.dict()
        )
        
    except Exception as e:
        logger.error(f"Failed to get memory statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve memory statistics"
        )


@router.get(
    "/entities",
    response_model=APIResponse,
    summary="Get memory entities",
    description="Get entities from the user's knowledge graph",
)
async def get_memory_entities(
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    entity_type: Optional[str] = Query(None, description="Filter by entity type"),
    limit: int = Query(default=50, ge=1, le=200),
    offset: int = Query(default=0, ge=0),
):
    """
    Get entities from the user's knowledge graph.
    
    Args:
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
        entity_type: Optional filter by entity type
        limit: Maximum number of entities to return
        offset: Number of entities to skip
    
    Returns:
        APIResponse: List of entities
    """
    try:
        # Get entities from Graphiti
        entities = await graphiti_service.get_entities(
            user_id=current_user.id,
            group_id=current_user.group_id,
            entity_type=entity_type,
            limit=limit,
            offset=offset
        )
        
        logger.info(
            "Retrieved memory entities",
            user_id=str(current_user.id),
            entity_type=entity_type,
            entity_count=len(entities),
            limit=limit,
            offset=offset
        )
        
        return APIResponse(
            success=True,
            message="Entities retrieved successfully",
            data={
                "entities": entities,
                "entity_type": entity_type,
                "total_count": len(entities),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get entities: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve entities"
        )


@router.get(
    "/relationships",
    response_model=APIResponse,
    summary="Get memory relationships",
    description="Get relationships from the user's knowledge graph",
)
async def get_memory_relationships(
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
    relationship_type: Optional[str] = Query(None, description="Filter by relationship type"),
    entity_id: Optional[UUID] = Query(None, description="Filter by entity ID"),
    limit: int = Query(default=50, ge=1, le=200),
    offset: int = Query(default=0, ge=0),
):
    """
    Get relationships from the user's knowledge graph.
    
    Args:
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
        relationship_type: Optional filter by relationship type
        entity_id: Optional filter by entity ID
        limit: Maximum number of relationships to return
        offset: Number of relationships to skip
    
    Returns:
        APIResponse: List of relationships
    """
    try:
        # Get relationships from Graphiti
        relationships = await graphiti_service.get_relationships(
            user_id=current_user.id,
            group_id=current_user.group_id,
            relationship_type=relationship_type,
            entity_id=entity_id,
            limit=limit,
            offset=offset
        )
        
        logger.info(
            "Retrieved memory relationships",
            user_id=str(current_user.id),
            relationship_type=relationship_type,
            entity_id=str(entity_id) if entity_id else None,
            relationship_count=len(relationships),
            limit=limit,
            offset=offset
        )
        
        return APIResponse(
            success=True,
            message="Relationships retrieved successfully",
            data={
                "relationships": relationships,
                "relationship_type": relationship_type,
                "entity_id": str(entity_id) if entity_id else None,
                "total_count": len(relationships),
                "limit": limit,
                "offset": offset
            }
        )
        
    except Exception as e:
        logger.error(f"Failed to get relationships: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve relationships"
        )


@router.delete(
    "/episodes/{episode_id}",
    response_model=APIResponse,
    summary="Delete memory episode",
    description="Delete a specific episode from memory",
)
async def delete_episode(
    episode_id: UUID,
    current_user: User = Depends(get_current_user),
    graphiti_service: GraphitiService = Depends(get_graphiti_service),
):
    """
    Delete a specific episode from memory.
    
    Args:
        episode_id: ID of the episode to delete
        current_user: Authenticated user
        graphiti_service: Graphiti memory service
    
    Returns:
        APIResponse: Deletion confirmation
    """
    try:
        # Delete episode from Graphiti
        success = await graphiti_service.delete_episode(
            episode_id=episode_id,
            user_id=current_user.id,
            group_id=current_user.group_id
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Episode not found"
            )
        
        logger.info(
            "Deleted memory episode",
            user_id=str(current_user.id),
            episode_id=str(episode_id)
        )
        
        return APIResponse(
            success=True,
            message="Episode deleted successfully"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete episode: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete episode"
        )
