"""
Workers package for Mandy AI Companion

This package contains background worker services that enhance <PERSON>'s capabilities:
- Content Fetcher: Retrieves and processes external content
- Reflection Engine: Analyzes conversations and updates trust models
"""

from .content_fetcher import ContentFetcher
from .reflection_engine import ReflectionEngine

__all__ = ["ContentFetcher", "ReflectionEngine"]
