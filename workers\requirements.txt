# Workers Requirements
# Mandy AI Companion - Background processing services

# --- Core Task & AI Libraries ---
google-generativeai==0.3.2
# Install graphiti-core directly from the GitHub repository for latest features
git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core
APScheduler==3.10.4  # For scheduling background jobs

# --- Communication & Utilities ---
httpx==0.25.2  # For async API calls to the Graphiti Server
loguru==0.7.2  # For structured logging
python-dotenv==1.0.0  # For environment configuration

# --- Neo4j Driver ---
neo4j==5.15.0

# --- External API Integrations ---
newsapi-python==0.2.7  # For news content fetching
praw==7.7.1  # For Reddit API integration
feedparser==6.0.10  # For RSS feed parsing
beautifulsoup4==4.12.2  # For web scraping
requests==2.31.0  # For HTTP requests

# --- Data Processing ---
python-dateutil==2.8.2
orjson==3.9.10

# --- System Utilities ---
psutil==5.9.6
