"""
Common Pydantic models used across the Mandy Core Chat Engine API.
"""

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """Base response model for all API responses."""

    success: bool = Field(..., description="Whether the request was successful")
    message: str = Field(..., description="Human-readable message about the response")
    request_id: Optional[str] = Field(None, description="Unique request identifier")


class ErrorResponse(BaseResponse):
    """Error response model for failed API requests."""

    success: bool = Field(False, description="Always false for error responses")
    errors: List[str] = Field(default_factory=list, description="List of error messages")
    error_code: Optional[str] = Field(None, description="Machine-readable error code")


class HealthResponse(BaseModel):
    """Health check response model."""

    status: str = Field(..., description="Service health status")
    version: str = Field(..., description="Service version")
    timestamp: str = Field(..., description="Current timestamp")
    services: Dict[str, Any] = Field(default_factory=dict, description="Status of dependent services")
    uptime_seconds: float = Field(..., description="Service uptime in seconds")


class PaginationMeta(BaseModel):
    """Pagination metadata for list responses."""

    page: int = Field(..., description="Current page number")
    per_page: int = Field(..., description="Items per page")
    total: int = Field(..., description="Total number of items")
    pages: int = Field(..., description="Total number of pages")
    has_next: bool = Field(..., description="Whether there is a next page")
    has_prev: bool = Field(..., description="Whether there is a previous page")


class PaginatedResponse(BaseResponse):
    """Base response model for paginated data."""

    data: List[Any] = Field(default_factory=list, description="List of items")
    meta: PaginationMeta = Field(..., description="Pagination metadata")
