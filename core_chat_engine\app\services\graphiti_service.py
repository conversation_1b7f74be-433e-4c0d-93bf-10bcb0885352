"""
Graphiti service for temporal knowledge graph operations.

This service handles all interactions with the Graphiti framework directly,
including episode management, memory search, and knowledge graph operations.
"""

import asyncio
import json
import os
import time
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import uuid4

from loguru import logger

try:
    from graphiti_core import Graphiti
    from graphiti_core.nodes import EpisodeType
    GRAPHITI_AVAILABLE = True
except ImportError:
    logger.warning("Graphiti not available - install with: pip install git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core")
    GRAPHITI_AVAILABLE = False


class GraphitiService:
    """Service for interacting with Graphiti temporal knowledge graph directly."""

    def __init__(self):
        """Initialize Graphiti service with direct client configuration."""
        if not GRAPHITI_AVAILABLE:
            raise ImportError("Graphiti is not available. Please install it first.")

        # Initialize Graphiti client
        self.graphiti = None
        self._initialized = False

        # Get configuration from environment
        self.neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
        self.neo4j_username = os.getenv("NEO4J_USERNAME", "neo4j")
        self.neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
        self.default_group_id = os.getenv("GRAPHITI_GROUP_ID", "mandy_default_group")

        logger.info("Graphiti service initialized")

    async def initialize(self):
        """Initialize the Graphiti client and ensure it's ready."""
        if not self._initialized:
            try:
                # Initialize Graphiti with Neo4j connection
                self.graphiti = Graphiti(
                    self.neo4j_uri,
                    self.neo4j_username,
                    self.neo4j_password
                )

                # Build indices and constraints (only needs to be done once)
                await self.graphiti.build_indices_and_constraints()

                self._initialized = True

                logger.info(
                    "Graphiti client initialized successfully",
                    extra={
                        "neo4j_uri": self.neo4j_uri,
                        "default_group_id": self.default_group_id
                    }
                )

            except Exception as e:
                logger.error(f"Failed to initialize Graphiti client: {str(e)}")
                raise

    async def _ensure_initialized(self):
        """Ensure Graphiti client is initialized."""
        if not self._initialized:
            await self.initialize()
    
    async def add_episode(self, group_id: str, content: dict) -> str:
        """
        Add an episode to the knowledge graph.

        Args:
            group_id: Group ID for memory isolation (typically user_id)
            content: Dictionary containing the interaction data

        Returns:
            str: Episode ID
        """
        start_time = time.time()
        episode_id = str(uuid4())

        try:
            # Ensure Graphiti is initialized
            await self._ensure_initialized()

            # Convert content to string for Graphiti
            episode_body = json.dumps(content) if isinstance(content, dict) else str(content)

            # Determine episode type
            episode_type = EpisodeType.json if isinstance(content, dict) else EpisodeType.text

            # Add episode to Graphiti
            await self.graphiti.add_episode(
                name=f"episode_{episode_id}",
                episode_body=episode_body,
                source=episode_type,
                source_description="mandy_conversation",
                reference_time=datetime.now(timezone.utc),
                group_id=group_id
            )

            duration_ms = (time.time() - start_time) * 1000

            logger.info(
                "Added episode to Graphiti",
                extra={
                    "episode_id": episode_id,
                    "group_id": group_id,
                    "content_length": len(episode_body),
                    "episode_type": episode_type.value,
                    "duration_ms": round(duration_ms, 2)
                }
            )

            return episode_id

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(
                f"Failed to add episode to Graphiti: {str(e)}",
                extra={
                    "episode_id": episode_id,
                    "group_id": group_id,
                    "duration_ms": round(duration_ms, 2)
                },
                exc_info=True
            )
            raise
    
    async def search_relevant_context(
        self,
        group_id: str,
        query: str,
        limit: int = 10
    ) -> Dict[str, Any]:
        """
        Search for relevant context from the knowledge graph.

        Args:
            group_id: Group ID for memory isolation (typically user_id)
            query: Search query
            limit: Maximum number of results

        Returns:
            Dictionary containing relevant memories and relationship data
        """
        start_time = time.time()

        try:
            # Ensure Graphiti is initialized
            await self._ensure_initialized()

            # Perform search using Graphiti
            search_results = await self.graphiti.search(
                query=query,
                group_id=group_id,
                limit=limit
            )

            # Convert results to our format
            memories = []
            relationships = []

            for result in search_results:
                # Extract memory information
                memory_item = {
                    "content": getattr(result, 'fact', str(result)),
                    "score": getattr(result, 'score', 1.0),
                    "timestamp": getattr(result, 'created_at', None),
                    "source": "graphiti",
                    "metadata": {
                        "uuid": str(getattr(result, 'uuid', '')),
                        "type": getattr(result, 'type', 'memory')
                    }
                }
                memories.append(memory_item)

                # Extract relationship information if available
                if hasattr(result, 'source_node_uuid') and hasattr(result, 'target_node_uuid'):
                    relationship = {
                        "source": str(result.source_node_uuid),
                        "target": str(result.target_node_uuid),
                        "type": getattr(result, 'relationship_type', 'related_to'),
                        "strength": getattr(result, 'score', 1.0)
                    }
                    relationships.append(relationship)

            duration_ms = (time.time() - start_time) * 1000

            # Structure the context response
            context = {
                "memories": memories,
                "relationships": relationships,
                "query": query,
                "total_results": len(memories),
                "search_metadata": {
                    "group_id": group_id,
                    "limit": limit,
                    "duration_ms": round(duration_ms, 2)
                }
            }

            logger.info(
                "Retrieved relevant context from Graphiti",
                extra={
                    "group_id": group_id,
                    "query": query,
                    "results_count": len(memories),
                    "relationships_count": len(relationships),
                    "duration_ms": round(duration_ms, 2)
                }
            )

            return context

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(
                f"Failed to search Graphiti: {str(e)}",
                extra={
                    "group_id": group_id,
                    "query": query,
                    "duration_ms": round(duration_ms, 2)
                },
                exc_info=True
            )
            return {
                "memories": [],
                "relationships": [],
                "query": query,
                "total_results": 0,
                "search_metadata": {
                    "group_id": group_id,
                    "limit": limit,
                    "duration_ms": round(duration_ms, 2),
                    "error": str(e)
                }
            }
    
    async def get_memory_statistics(self, group_id: str) -> Dict[str, Any]:
        """
        Get memory statistics for a group.

        Args:
            group_id: Group ID for memory isolation

        Returns:
            Dictionary with memory statistics
        """
        try:
            # For now, return basic statistics
            # In the future, this could query Graphiti for actual statistics
            return {
                "group_id": group_id,
                "total_episodes": 0,
                "total_nodes": 0,
                "total_edges": 0,
                "memory_size_mb": 0.0,
                "last_updated": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error(
                f"Failed to get memory statistics: {str(e)}",
                extra={"group_id": group_id},
                exc_info=True
            )
            return {
                "group_id": group_id,
                "error": str(e)
            }

    async def close(self):
        """Close the Graphiti client and clean up resources."""
        try:
            if self.graphiti and hasattr(self.graphiti, 'close'):
                await self.graphiti.close()
            self._initialized = False
            logger.info("Graphiti service closed")
        except Exception as e:
            logger.error(f"Error closing Graphiti service: {str(e)}", exc_info=True)


# Singleton instance
_graphiti_service: Optional[GraphitiService] = None


def get_graphiti_service() -> GraphitiService:
    """Get the Graphiti service instance (singleton)."""
    global _graphiti_service
    if _graphiti_service is None:
        _graphiti_service = GraphitiService()
    return _graphiti_service
