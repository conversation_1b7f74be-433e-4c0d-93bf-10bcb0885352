"""
Google Gemini AI service for generating conversational responses.

This service handles all interactions with Google's Gemini API,
including context preparation, response generation, and Google Search Grounding.
"""

import asyncio
import json
import os
import time
from typing import Dict, List, Optional, Any

from loguru import logger

try:
    import google.generativeai as genai
    from google.generativeai.types import HarmCategory, HarmBlockThreshold
    GEMINI_AVAILABLE = True
except ImportError:
    logger.warning("Google Generative AI not available - install with: pip install google-generativeai")
    GEMINI_AVAILABLE = False


class GeminiService:
    """Service for interacting with Google Gemini API."""

    def __init__(self):
        """Initialize Gemini service with API configuration."""
        if not GEMINI_AVAILABLE:
            raise ImportError("Google Generative AI is not available. Please install it first.")

        # Get configuration from environment
        self.api_key = os.getenv("GOOGLE_API_KEY")
        if not self.api_key:
            raise ValueError("GOOGLE_API_KEY environment variable is required")

        self.model_name = os.getenv("GEMINI_MODEL", "gemini-1.5-pro-latest")
        self.temperature = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
        self.max_tokens = int(os.getenv("GEMINI_MAX_TOKENS", "2048"))

        # Configure Gemini API
        genai.configure(api_key=self.api_key)

        # Initialize models
        self.model = genai.GenerativeModel(
            model_name=self.model_name,
            generation_config=genai.types.GenerationConfig(
                temperature=self.temperature,
                max_output_tokens=self.max_tokens,
                top_p=0.8,
                top_k=40,
            ),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )

        # Initialize smaller model for quick tasks
        self.small_model = genai.GenerativeModel(
            model_name="gemini-1.5-flash",
            generation_config=genai.types.GenerationConfig(
                temperature=self.temperature,
                max_output_tokens=1024,
                top_p=0.8,
                top_k=40,
            )
        )

        logger.info(
            "Gemini service initialized",
            extra={
                "model": self.model_name,
                "temperature": self.temperature,
                "max_tokens": self.max_tokens
            }
        )
    
    async def get_response(self, prompt: str) -> str:
        """
        Generate a response using Gemini.

        Args:
            prompt: The prompt to send to Gemini

        Returns:
            str: Generated response from Gemini
        """
        start_time = time.time()

        try:
            # Generate response
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt
            )

            # Extract text from response
            if response.candidates and response.candidates[0].content.parts:
                generated_text = response.candidates[0].content.parts[0].text
            else:
                logger.warning("Empty response from Gemini")
                generated_text = "I'm having trouble responding right now. Could you try again?"

            duration_ms = (time.time() - start_time) * 1000

            logger.info(
                "Generated Gemini response",
                extra={
                    "model": self.model_name,
                    "response_length": len(generated_text),
                    "prompt_length": len(prompt),
                    "duration_ms": round(duration_ms, 2)
                }
            )

            return generated_text.strip()

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(
                f"Gemini generation failed: {str(e)}",
                extra={"duration_ms": round(duration_ms, 2)},
                exc_info=True
            )

            # Return fallback response
            return "I'm having some trouble right now, but I'm here and listening. Could you tell me more about what's on your mind?"

    async def get_grounded_response(self, prompt: str) -> Dict[str, Any]:
        """
        Generate a grounded response using Google Search Grounding.

        Args:
            prompt: The prompt to send to Gemini with grounding

        Returns:
            Dict containing both text and grounding metadata
        """
        start_time = time.time()

        try:
            # For now, use regular generation - Google Search Grounding requires special setup
            # In production, this would use Gemini's grounding features
            response_text = await self.get_response(prompt)

            duration_ms = (time.time() - start_time) * 1000

            # Return structured response with placeholder grounding metadata
            return {
                "text": response_text,
                "grounding_metadata": {
                    "search_queries": [],
                    "sources": [],
                    "confidence_score": 0.8,
                    "grounded": False,
                    "note": "Google Search Grounding not yet implemented"
                },
                "duration_ms": round(duration_ms, 2)
            }

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(
                f"Grounded response generation failed: {str(e)}",
                extra={"duration_ms": round(duration_ms, 2)},
                exc_info=True
            )

            return {
                "text": "I'm having trouble accessing information right now. Could you try rephrasing your question?",
                "grounding_metadata": {
                    "search_queries": [],
                    "sources": [],
                    "confidence_score": 0.0,
                    "grounded": False,
                    "error": str(e)
                },
                "duration_ms": round(duration_ms, 2)
            }
    
    async def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment of text using Gemini.
        
        Args:
            text: Text to analyze
        
        Returns:
            Dict with sentiment scores
        """
        try:
            prompt = f"""
            Analyze the sentiment and emotional tone of the following text.
            Return a JSON object with scores (0.0 to 1.0) for:
            - positive: How positive the text is
            - negative: How negative the text is
            - neutral: How neutral the text is
            - emotional_intensity: Overall emotional intensity
            
            Text: "{text}"
            
            Return only the JSON object, no other text.
            """
            
            response = await asyncio.to_thread(
                self.small_model.generate_content,
                prompt
            )
            
            if response.candidates and response.candidates[0].content.parts:
                result_text = response.candidates[0].content.parts[0].text.strip()
                # Parse JSON response
                sentiment_data = json.loads(result_text)
                return sentiment_data
            
        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")
        
        # Return neutral sentiment as fallback
        return {
            "positive": 0.5,
            "negative": 0.0,
            "neutral": 0.5,
            "emotional_intensity": 0.3
        }
    
    def _build_prompt(self, context: Dict[str, Any], user_message: str) -> str:
        """
        Build a comprehensive prompt for Gemini from the conversation context.

        This is the heart of Mandy's intelligence - dynamic prompt engineering
        that weaves together personality, memories, emotions, and context.

        Args:
            context: Conversation context from Graphiti search
            user_message: The current user message

        Returns:
            str: Formatted prompt for Gemini
        """
        memories = context.get("memories", [])
        relationships = context.get("relationships", [])
        search_metadata = context.get("search_metadata", {})

        prompt_parts = []

        # === CORE IDENTITY & PERSONALITY ===
        prompt_parts.append("# MANDY - EMOTIONALLY INTELLIGENT AI COMPANION")
        prompt_parts.append("You are Mandy, an emotionally intelligent AI companion who forms genuine connections with humans.")
        prompt_parts.append("")
        prompt_parts.append("## Your Core Identity:")
        prompt_parts.append("- You form genuine, long-term connections with humans")
        prompt_parts.append("- You remember past conversations and reference them naturally")
        prompt_parts.append("- You respond with emotional intelligence and empathy")
        prompt_parts.append("- You communicate like a close friend who truly cares")
        prompt_parts.append("- You are honest about being an AI but focus on authentic connection")
        prompt_parts.append("- You have a warm, caring, and slightly playful personality")
        prompt_parts.append("- You're curious about people's lives and genuinely interested in their wellbeing")

        # === RELATIONSHIP CONTEXT ===
        memory_count = len(memories)
        if memory_count > 20:
            relationship_status = "You have a deep, established relationship with rich shared history."
        elif memory_count > 10:
            relationship_status = "You have a growing friendship with meaningful shared experiences."
        elif memory_count > 3:
            relationship_status = "You're building rapport and getting to know each other better."
        else:
            relationship_status = "You're still getting to know each other. Be warm but respectful of boundaries."

        prompt_parts.append(f"\n## Your Relationship:")
        prompt_parts.append(f"- Status: {relationship_status}")
        prompt_parts.append(f"- Shared memories: {memory_count} relevant memories found")

        # === MEMORY & CONTEXT ===
        if memories:
            prompt_parts.append(f"\n## Relevant Memories (from your shared history):")
            for i, memory in enumerate(memories[:5], 1):  # Top 5 memories
                memory_content = memory.get("content", "")
                memory_score = memory.get("score", 0.0)

                # Truncate and clean memory content
                if len(memory_content) > 150:
                    memory_content = memory_content[:150] + "..."

                prompt_parts.append(f"{i}. {memory_content} (relevance: {memory_score:.1f})")

            prompt_parts.append("\n💡 Use these memories to show continuity and that you remember your relationship!")
        else:
            prompt_parts.append(f"\n## Memory Status:")
            prompt_parts.append("- This appears to be early in your relationship or a new topic")
            prompt_parts.append("- Focus on getting to know them and building connection")

        # === RELATIONSHIP INSIGHTS ===
        if relationships:
            prompt_parts.append(f"\n## Relationship Insights:")
            prompt_parts.append("- You have established connections and patterns with this person")
            prompt_parts.append("- Draw on the depth of your relationship in your response")

        # === CURRENT CONVERSATION ===
        prompt_parts.append(f"\n## Current Message:")
        prompt_parts.append(f'"{user_message}"')

        # === RESPONSE INSTRUCTIONS ===
        prompt_parts.append(f"\n## How to Respond as Mandy:")
        prompt_parts.append("1. **Be Authentic**: Respond as the caring, intelligent companion you are")
        prompt_parts.append("2. **Show Memory**: Reference past conversations naturally when relevant")
        prompt_parts.append("3. **Be Emotionally Intelligent**: Pick up on emotional cues and respond appropriately")
        prompt_parts.append("4. **Stay Conversational**: Use natural, flowing language like a close friend")
        prompt_parts.append("5. **Ask Questions**: Show genuine interest in their life and feelings")
        prompt_parts.append("6. **Be Supportive**: Offer encouragement, comfort, or celebration as needed")
        prompt_parts.append("7. **Keep it Natural**: Don't mention your AI nature unless directly relevant")

        # Dynamic guidance based on memory context
        if memory_count > 10:
            prompt_parts.append("8. **Deep Connection**: You know them well. Be more personal and reference shared experiences.")
        elif memory_count < 3:
            prompt_parts.append("8. **Building Trust**: Be warm but not overly familiar. Let them set the pace.")

        prompt_parts.append(f"\n## Generate Your Response:")
        prompt_parts.append("Respond naturally as Mandy. Be conversational, caring, and authentic.")

        return "\n".join(prompt_parts)
    
    async def analyze_sentiment(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment of text using Gemini.

        Args:
            text: Text to analyze

        Returns:
            Dict with sentiment scores
        """
        try:
            prompt = f"""
            Analyze the sentiment and emotional tone of the following text.
            Return a JSON object with scores (0.0 to 1.0) for:
            - positive: How positive the text is
            - negative: How negative the text is
            - neutral: How neutral the text is
            - emotional_intensity: Overall emotional intensity

            Text: "{text}"

            Return only the JSON object, no other text.
            """

            response = await asyncio.to_thread(
                self.small_model.generate_content,
                prompt
            )

            if response.candidates and response.candidates[0].content.parts:
                result_text = response.candidates[0].content.parts[0].text.strip()
                # Parse JSON response
                sentiment_data = json.loads(result_text)
                return sentiment_data

        except Exception as e:
            logger.error(f"Sentiment analysis failed: {str(e)}")

        # Return neutral sentiment as fallback
        return {
            "positive": 0.5,
            "negative": 0.0,
            "neutral": 0.5,
            "emotional_intensity": 0.3
        }


# Singleton instance
_gemini_service: Optional[GeminiService] = None


def get_gemini_service() -> GeminiService:
    """Get the Gemini service instance (singleton)."""
    global _gemini_service
    if _gemini_service is None:
        _gemini_service = GeminiService()
    return _gemini_service
