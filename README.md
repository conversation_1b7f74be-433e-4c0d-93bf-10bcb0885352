# 💝 Mandy v1.0 - Emotionally Intelligent AI Companion

Mandy is a production-ready AI companion system that forms genuine, long-term connections with users through emotional intelligence, persistent memory, and authentic conversation.

## 🌟 Key Features

- **Emotional Intelligence**: Responds with empathy and emotional awareness
- **Persistent Memory**: Remembers conversations and builds context over time using Graphiti
- **Human-like Interaction**: Natural typing patterns and conversational flow
- **Scalable Architecture**: Microservices design with Docker containerization
- **Production Ready**: Comprehensive logging, health checks, and error handling

## 🏗️ Architecture

### Core Components

1. **Core Chat Engine** (FastAPI)
   - Main conversational AI service
   - Orchestrates the cognitive loop between Gemini AI and Graphiti memory
   - Handles authentication, chat processing, and API endpoints

2. **Graphiti Memory Framework**
   - Temporal knowledge graph for persistent memory
   - Stores and retrieves conversation context
   - Enables relationship building over time

3. **Google Gemini AI**
   - Advanced language model for response generation
   - Sophisticated prompt engineering for emotional intelligence
   - Optional Google Search Grounding for factual queries

4. **Gradio UI**
   - Web-based chat interface
   - Real-time conversation with typing indicators
   - Simple authentication system

5. **Background Workers**
   - Content Fetcher: Enriches knowledge base
   - Reflection Engine: Analyzes conversations and updates trust models

6. **Neo4j Database**
   - Graph database backend for Graphiti
   - Stores relationships and temporal data

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose
- Google Gemini API key
- Neo4j database (included in docker-compose)

### Setup

1. **Clone and configure**:
   ```bash
   git clone <repository-url>
   cd mandy
   cp .env.example .env
   ```

2. **Configure environment variables**:
   ```bash
   # Edit .env file with your settings
   GOOGLE_API_KEY=your_google_api_key_here
   NEO4J_PASSWORD=your_secure_password_here
   ```

3. **Start the system**:
   ```bash
   docker-compose up -d
   ```

4. **Access Mandy**:
   - UI: http://localhost:7860
   - API: http://localhost:8000
   - Neo4j Browser: http://localhost:7474

### Demo Credentials

- Username: `user`
- Password: `password`

## 📁 Project Structure

```
mandy/
├── .env.example                 # Environment configuration template
├── docker-compose.yml          # Docker services orchestration
├── README.md                   # This file
│
├── core_chat_engine/           # Main FastAPI application
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app/
│       ├── main.py             # FastAPI application entry point
│       ├── models/             # Pydantic data models
│       ├── routes/             # API route handlers
│       │   ├── auth.py         # Authentication endpoints
│       │   ├── chat.py         # Core chat functionality
│       │   └── health.py       # Health check endpoints
│       └── services/           # Business logic services
│           ├── gemini_service.py    # Google Gemini AI integration
│           ├── graphiti_service.py  # Graphiti memory management
│           └── typing_engine.py     # Human-like response formatting
│
├── ui/                         # Gradio web interface
│   ├── Dockerfile
│   ├── requirements.txt
│   └── app.py                  # Gradio application
│
└── workers/                    # Background worker services
    ├── Dockerfile
    ├── requirements.txt
    ├── content_fetcher.py      # Content enrichment worker
    └── reflection_engine.py    # Conversation analysis worker
```

## 🔧 Configuration

### Environment Variables

Key configuration options in `.env`:

```bash
# Core Application
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# Google Gemini AI
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_MODEL=gemini-1.5-pro-latest
GEMINI_TEMPERATURE=0.7

# Neo4j Database
NEO4J_URI=bolt://neo4j:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_neo4j_password_here

# Authentication (Demo)
DEMO_USERNAME=user
DEMO_PASSWORD=password

# Typing Engine
TYPING_MIN_DELAY=0.5
TYPING_MAX_DELAY=2.0
```

## 🧠 How Mandy Works

### The Cognitive Loop

1. **Message Reception**: User sends a message through the UI
2. **Context Retrieval**: Graphiti searches for relevant memories and relationships
3. **Prompt Engineering**: Dynamic prompt construction with personality, memories, and emotional context
4. **AI Generation**: Gemini generates an emotionally intelligent response
5. **Response Formatting**: Typing engine creates human-like message bursts
6. **Memory Storage**: Conversation is stored in Graphiti for future context
7. **Background Processing**: Workers analyze patterns and update trust models

### Memory Architecture

Mandy uses Graphiti's temporal knowledge graph to:
- Store conversation episodes with timestamps
- Build relationship networks between concepts
- Retrieve contextually relevant memories
- Track emotional patterns and trust development

## 🛠️ Development

### Running in Development Mode

```bash
# Start with hot reload
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

# View logs
docker-compose logs -f core-chat-engine

# Access services
curl http://localhost:8000/health
```

### API Endpoints

- `GET /health` - Service health check
- `POST /auth/login` - User authentication
- `POST /chat/` - Send message to Mandy
- `GET /chat/status` - Chat service status

## 🔒 Security Notes

This is a demo system with simplified authentication. For production:

- Implement proper JWT authentication
- Use secure password hashing
- Add rate limiting
- Configure HTTPS
- Secure Neo4j with proper credentials
- Validate and sanitize all inputs

## 🚀 Production Deployment

For production deployment:

1. Use environment-specific configurations
2. Set up proper monitoring and logging
3. Configure load balancing
4. Implement backup strategies for Neo4j
5. Set up CI/CD pipelines
6. Configure proper secrets management

## 🤝 Contributing

This is a complete implementation of the Mandy AI companion system. The codebase demonstrates:

- Modern Python async/await patterns
- Microservices architecture
- Production-ready error handling
- Comprehensive logging
- Docker containerization
- API design best practices

## 📄 License

This project is provided as a complete implementation example for educational and development purposes.

---

**Built with ❤️ using FastAPI, Graphiti, Google Gemini AI, and Neo4j**

## 🏗️ Architecture Overview

Mandy is composed of independently deployable microservices:

```
┌───────────────────────────────┐
│       Gradio Web UI           │
│ (User ID + Password Auth)     │
└──────────────┬────────────────┘
               | (Secure API Call)
               ↓
┌───────────────────────────────────────┐
│      Core Chat Engine (Gemini)        │
│   - Orchestrates all logic            │
│   - Manages Typing & Tone             │
└──────────────┬────────────────┘
               | (Graphiti REST API Call)
               ↓
┌───────────────────────────────────────────────┐
│          Graphiti Server (REST/MCP)           │
│    (The single entrypoint for all memory)     │
└───────────────────────┬───────────────────────┘
                        | (Native Driver Connection)
                        ↓
      ┌───────────────────────────────┐
      │     Neo4j Database Instance   │
      │ (Mandy's Physical Brain)      │
      └───────────────────────────────┘
```

## 🧠 Core Technology Stack

- **Intelligence Engine**: Google Gemini 1.5 Pro/Flash
- **Memory System**: Graphiti Framework with Neo4j
- **API Framework**: Python FastAPI
- **UI Framework**: Gradio
- **Background Processing**: APScheduler
- **Containerization**: Docker & Docker Compose

## 🚀 Quick Start

### Prerequisites

- Python 3.10+
- Docker & Docker Compose
- Google Gemini API Key
- Neo4j Database (or use Docker setup)

### Environment Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd mandy
```

2. Copy environment configuration:
```bash
cp .env.example .env
```

3. Edit `.env` with your API keys and configuration:
   - Set `GEMINI_API_KEY` to your Google Gemini API key
   - Set `SECRET_KEY` to a secure random string
   - Configure Neo4j credentials if using external database

4. **Quick Development Start:**
```bash
python start_dev.py
```
This script will:
- Check your environment
- Install dependencies
- Start Neo4j in Docker
- Launch the Core Chat Engine
- Provide testing instructions

5. **Manual Docker Setup:**
```bash
docker-compose up -d
```

6. **Test the API:**
```bash
python test_api.py
```

### Access Points
- Core API: http://localhost:8000
- API Documentation: http://localhost:8000/docs
- Health Check: http://localhost:8000/health
- Neo4j Browser: http://localhost:7474 (neo4j/password)

## 📁 Project Structure

```
mandy/
├── core_chat_engine/          # Main conversation orchestrator
│   ├── app/
│   ├── models/
│   ├── services/
│   └── Dockerfile
├── workers/                   # Background processing services
│   ├── content_fetcher/
│   ├── reflection_engine/
│   └── Dockerfile
├── ui/                        # Gradio web interface
│   ├── components/
│   ├── auth/
│   └── Dockerfile
├── shared/                    # Shared utilities and models
│   ├── auth/
│   ├── models/
│   └── utils/
├── docker-compose.yml         # Local development setup
├── docker-compose.prod.yml    # Production deployment
├── .env.example              # Environment template
└── README.md
```

## 🔧 Configuration

### Environment Variables

Key environment variables (see `.env.example` for complete list):

- `GEMINI_API_KEY`: Google Gemini API key
- `NEO4J_URI`: Neo4j database connection
- `NEO4J_USER`: Neo4j username
- `NEO4J_PASSWORD`: Neo4j password
- `SECRET_KEY`: JWT secret for authentication
- `ENVIRONMENT`: deployment environment (dev/staging/prod)

### Service Configuration

Each service can be configured independently:
- Core Chat Engine: Port 8000
- Workers: Background processes
- UI: Port 7860
- Neo4j: Port 7687 (bolt), 7474 (browser)

## 🧪 Development

### What's Currently Implemented

✅ **Core Chat Engine** (Complete)
- FastAPI-based REST API with full authentication
- Google Gemini integration for AI responses
- Graphiti framework integration for temporal memory
- Emotion analysis and sentiment detection
- Human-like typing engine with message bursts
- Comprehensive logging and monitoring
- Health checks and error handling

✅ **Project Infrastructure** (Complete)
- Docker containerization for all services
- Environment configuration management
- Production-ready deployment configs
- Comprehensive test suite
- Development tools and scripts

🚧 **In Progress**
- Background worker services
- Gradio web interface
- Advanced memory operations

### Local Development

1. **Quick Start (Recommended):**
```bash
python start_dev.py
```

2. **Manual Setup:**
```bash
# Install dependencies
pip install -r requirements.txt

# Start Neo4j
docker run -d --name mandy-neo4j-dev -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/password neo4j:5.26

# Start Core Chat Engine
cd core_chat_engine && uvicorn app.main:app --reload --port 8000
```

### Testing

```bash
# Run comprehensive test suite
pytest tests/ -v

# Test API endpoints
python test_api.py

# Manual API testing
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

### API Usage Examples

1. **Login and get token:**
```bash
curl -X POST "http://localhost:8000/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

2. **Send a message to Mandy:**
```bash
curl -X POST "http://localhost:8000/chat/send" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello Mandy, how are you today?"}'
```

3. **Check memory stats:**
```bash
curl -X GET "http://localhost:8000/memory/stats" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 🚀 Deployment

### Free-Tier Platforms

Mandy is designed for deployment on free-tier platforms:

- **Render**: Use `render.yaml` configuration
- **Railway**: Use `railway.json` configuration
- **Hugging Face Spaces**: Use `spaces/` configuration

### Production Deployment

1. Use production Docker Compose:
```bash
docker-compose -f docker-compose.prod.yml up -d
```

2. Configure environment for production
3. Set up monitoring and logging
4. Configure SSL/TLS certificates

## 🔒 Security

- JWT-based authentication
- API key management
- Secure inter-service communication
- User data isolation via group_id
- Environment-based configuration

## 📊 Monitoring

- Health checks for all services
- Structured logging with correlation IDs
- Performance metrics
- Error tracking and alerting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

[License information]

## 🆘 Support

- Documentation: [Link to docs]
- Issues: [GitHub Issues]
- Discord: [Community Discord]

---

Built with ❤️ for authentic AI companionship
