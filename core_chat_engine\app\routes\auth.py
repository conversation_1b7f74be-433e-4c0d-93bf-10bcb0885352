"""
Authentication routes for user login and token management.

This module provides a simple demo authentication system for the Mandy AI companion.
In production, this would be replaced with a proper authentication system.
"""

import os
from datetime import datetime, timedelta
from typing import Optional

from fastapi import APIRouter, HTTPException, status
from loguru import logger

from app.models.auth import LoginRequest, LoginResponse, User


# Initialize router
router = APIRouter()

# Demo credentials from environment
DEMO_USERNAME = os.getenv("DEMO_USERNAME", "user")
DEMO_PASSWORD = os.getenv("DEMO_PASSWORD", "password")


def create_demo_token(username: str) -> str:
    """Create a simple demo token (not secure - for demo only)."""
    import base64
    import json

    token_data = {
        "username": username,
        "exp": (datetime.utcnow() + timedelta(hours=24)).isoformat(),
        "iat": datetime.utcnow().isoformat()
    }

    # Simple base64 encoding (NOT SECURE - demo only)
    token_json = json.dumps(token_data)
    token_bytes = token_json.encode('utf-8')
    token = base64.b64encode(token_bytes).decode('utf-8')

    return token


def verify_demo_token(token: str) -> Optional[dict]:
    """Verify a demo token (not secure - for demo only)."""
    try:
        import base64
        import json

        token_bytes = base64.b64decode(token.encode('utf-8'))
        token_json = token_bytes.decode('utf-8')
        token_data = json.loads(token_json)

        # Check expiration
        exp_time = datetime.fromisoformat(token_data['exp'])
        if datetime.utcnow() > exp_time:
            return None

        return token_data

    except Exception:
        return None


@router.post(
    "/login",
    response_model=LoginResponse,
    summary="Demo user login",
    description="Authenticate demo user and return access token",
)
async def login(request: LoginRequest):
    """
    Authenticate demo user and return access token.

    This is a simplified demo authentication system.
    In production, this would validate against a real user database.

    Args:
        request: Login request with username and password

    Returns:
        LoginResponse: Response with token and user information
    """
    try:
        # Simple demo authentication
        if request.username == DEMO_USERNAME and request.password == DEMO_PASSWORD:
            # Create demo token
            access_token = create_demo_token(request.username)

            # Create demo user
            user = User(
                user_id="demo_user_123",
                username=request.username,
                email="<EMAIL>",
                full_name="Demo User",
                is_active=True,
                created_at=datetime.utcnow(),
                trust_level=0.5,
                conversation_count=0
            )

            logger.info(f"Demo user logged in: {request.username}")

            return LoginResponse(
                success=True,
                message="Login successful",
                access_token=access_token,
                token_type="bearer",
                expires_in=24 * 60 * 60,  # 24 hours
                user=user
            )
        else:
            logger.warning(f"Failed login attempt for username: {request.username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect username or password",
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed",
        )


@router.get(
    "/status",
    summary="Authentication status",
    description="Check authentication service status",
)
async def auth_status():
    """
    Check the status of the authentication service.

    Returns:
        Dict with authentication service status
    """
    return {
        "status": "ready",
        "auth_type": "demo",
        "demo_credentials": {
            "username": DEMO_USERNAME,
            "note": "This is a demo authentication system"
        },
        "timestamp": datetime.utcnow().isoformat()
    }
