"""
JWT token handling utilities for Mandy AI Companion.

This module provides secure JWT token creation, validation, and management
for authentication across all services.
"""

import os
from datetime import datetime, timedelta
from typing import Any, Dict, Optional
from uuid import UUID

import jwt
from passlib.context import CryptContext

from shared.models.base import User, UserRole


class JWTHandler:
    """Handles JWT token operations for authentication."""
    
    def __init__(self, secret_key: Optional[str] = None):
        """Initialize JWT handler with secret key."""
        self.secret_key = secret_key or os.getenv("SECRET_KEY")
        if not self.secret_key:
            raise ValueError("SECRET_KEY environment variable is required")
        
        self.algorithm = "HS256"
        self.access_token_expire_minutes = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "60"))
        self.refresh_token_expire_days = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))
        
        # Password hashing
        self.pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    def create_access_token(self, user: User, expires_delta: Optional[timedelta] = None) -> str:
        """Create a new access token for the user."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=self.access_token_expire_minutes)
        
        to_encode = {
            "sub": str(user.id),
            "username": user.username,
            "role": user.role.value,
            "group_id": user.group_id,
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "access"
        }
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def create_refresh_token(self, user: User) -> str:
        """Create a new refresh token for the user."""
        expire = datetime.utcnow() + timedelta(days=self.refresh_token_expire_days)
        
        to_encode = {
            "sub": str(user.id),
            "exp": expire,
            "iat": datetime.utcnow(),
            "type": "refresh"
        }
        
        return jwt.encode(to_encode, self.secret_key, algorithm=self.algorithm)
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode a JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            return payload
        except jwt.ExpiredSignatureError:
            raise ValueError("Token has expired")
        except jwt.InvalidTokenError:
            raise ValueError("Invalid token")
    
    def get_user_id_from_token(self, token: str) -> UUID:
        """Extract user ID from a valid token."""
        payload = self.verify_token(token)
        user_id = payload.get("sub")
        if not user_id:
            raise ValueError("Token does not contain user ID")
        return UUID(user_id)
    
    def get_user_info_from_token(self, token: str) -> Dict[str, Any]:
        """Extract user information from a valid token."""
        payload = self.verify_token(token)
        return {
            "user_id": UUID(payload.get("sub")),
            "username": payload.get("username"),
            "role": payload.get("role"),
            "group_id": payload.get("group_id"),
            "token_type": payload.get("type", "access")
        }
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return self.pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return self.pwd_context.verify(plain_password, hashed_password)
    
    def is_token_expired(self, token: str) -> bool:
        """Check if a token is expired without raising an exception."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            exp = payload.get("exp")
            if exp:
                return datetime.utcnow() > datetime.fromtimestamp(exp)
            return True
        except jwt.InvalidTokenError:
            return True
    
    def refresh_access_token(self, refresh_token: str) -> str:
        """Create a new access token using a valid refresh token."""
        payload = self.verify_token(refresh_token)
        
        if payload.get("type") != "refresh":
            raise ValueError("Invalid token type for refresh")
        
        user_id = UUID(payload.get("sub"))
        # Note: In a real implementation, you'd fetch the user from the database
        # For now, we'll create a minimal user object
        # This should be replaced with actual user lookup
        raise NotImplementedError("User lookup required for token refresh")


class AuthenticationError(Exception):
    """Custom exception for authentication errors."""
    pass


class AuthorizationError(Exception):
    """Custom exception for authorization errors."""
    pass


def require_role(required_role: UserRole):
    """Decorator to require a specific role for access."""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # This would be implemented with the actual user context
            # For now, it's a placeholder
            return func(*args, **kwargs)
        return wrapper
    return decorator


def get_current_user_id(token: str) -> UUID:
    """Utility function to get current user ID from token."""
    jwt_handler = JWTHandler()
    return jwt_handler.get_user_id_from_token(token)


def get_current_user_info(token: str) -> Dict[str, Any]:
    """Utility function to get current user info from token."""
    jwt_handler = JWTHandler()
    return jwt_handler.get_user_info_from_token(token)
