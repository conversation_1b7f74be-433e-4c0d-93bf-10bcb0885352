"""
Authentication-related Pydantic models for the Mandy Core Chat Engine API.
"""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field, EmailStr

from .common import BaseResponse


class LoginRequest(BaseModel):
    """Request model for user login."""

    username: str = Field(..., description="Username for authentication", min_length=3, max_length=50)
    password: str = Field(..., description="Password for authentication", min_length=6, max_length=100)


class TokenData(BaseModel):
    """Token payload data."""

    user_id: str = Field(..., description="User identifier")
    username: str = Field(..., description="Username")
    exp: datetime = Field(..., description="Token expiration time")
    iat: datetime = Field(..., description="Token issued at time")


class User(BaseModel):
    """User model for authentication and profile."""

    user_id: str = Field(..., description="Unique user identifier")
    username: str = Field(..., description="Username")
    email: Optional[EmailStr] = Field(None, description="User email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    is_active: bool = Field(True, description="Whether the user account is active")
    created_at: datetime = Field(..., description="When the user account was created")
    last_login: Optional[datetime] = Field(None, description="Last login timestamp")
    trust_level: float = Field(0.0, description="Overall trust level with Mandy (0.0-1.0)")
    conversation_count: int = Field(0, description="Total number of conversations")

    class Config:
        from_attributes = True


class LoginResponse(BaseResponse):
    """Response model for successful login."""

    access_token: str = Field(..., description="JWT access token")
    token_type: str = Field("bearer", description="Token type")
    expires_in: int = Field(..., description="Token expiration time in seconds")
    user: User = Field(..., description="User information")


class UserProfile(BaseModel):
    """Extended user profile information."""

    user_id: str = Field(..., description="Unique user identifier")
    username: str = Field(..., description="Username")
    email: Optional[EmailStr] = Field(None, description="User email address")
    full_name: Optional[str] = Field(None, description="User's full name")
    bio: Optional[str] = Field(None, description="User biography")
    preferences: dict = Field(default_factory=dict, description="User preferences")
    trust_level: float = Field(0.0, description="Overall trust level with Mandy")
    conversation_count: int = Field(0, description="Total number of conversations")
    total_messages: int = Field(0, description="Total number of messages sent")
    created_at: datetime = Field(..., description="Account creation date")
    last_active: Optional[datetime] = Field(None, description="Last activity timestamp")


class PasswordChangeRequest(BaseModel):
    """Request model for password change."""

    current_password: str = Field(..., description="Current password")
    new_password: str = Field(..., description="New password", min_length=6, max_length=100)
    confirm_password: str = Field(..., description="Confirmation of new password")
