#!/usr/bin/env python3
"""
Development startup script for Mandy AI Companion.

This script helps start the development environment with proper configuration
and provides helpful information for testing the system.
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def check_environment():
    """Check if the development environment is properly configured."""
    print("🔍 Checking development environment...")
    
    # Check Python version
    if sys.version_info < (3, 10):
        print("❌ Python 3.10 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check if .env file exists
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found. Creating from template...")
        env_example = Path(".env.example")
        if env_example.exists():
            env_file.write_text(env_example.read_text())
            print("✅ Created .env file from template")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file found")
    
    # Check required environment variables
    required_vars = ["GEMINI_API_KEY", "SECRET_KEY", "NEO4J_URI", "NEO4J_USER", "NEO4J_PASSWORD"]
    missing_vars = []
    
    # Load .env file
    try:
        with open(".env", "r") as f:
            env_content = f.read()
            for var in required_vars:
                if f"{var}=" not in env_content or f"{var}=your_" in env_content:
                    missing_vars.append(var)
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False
    
    if missing_vars:
        print(f"⚠️  Missing or placeholder values for: {', '.join(missing_vars)}")
        print("Please update your .env file with actual values")
        return False
    
    print("✅ Environment variables configured")
    return True


def start_neo4j():
    """Start Neo4j using Docker if not already running."""
    print("\n🗄️  Starting Neo4j database...")
    
    try:
        # Check if Neo4j is already running
        result = subprocess.run(
            ["docker", "ps", "--filter", "name=neo4j", "--format", "{{.Names}}"],
            capture_output=True,
            text=True
        )
        
        if "neo4j" in result.stdout:
            print("✅ Neo4j is already running")
            return True
        
        # Start Neo4j container
        cmd = [
            "docker", "run", "-d",
            "--name", "mandy-neo4j-dev",
            "-p", "7474:7474",
            "-p", "7687:7687",
            "-e", "NEO4J_AUTH=neo4j/password",
            "-e", "NEO4J_apoc_export_file_enabled=true",
            "-e", "NEO4J_apoc_import_file_enabled=true",
            "neo4j:5.26"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Neo4j container started")
            print("⏳ Waiting for Neo4j to be ready...")
            time.sleep(10)  # Give Neo4j time to start
            return True
        else:
            print(f"❌ Failed to start Neo4j: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Docker not found. Please install Docker or start Neo4j manually")
        return False
    except Exception as e:
        print(f"❌ Error starting Neo4j: {e}")
        return False


def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing dependencies...")
    
    try:
        # Install main requirements
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Dependencies installed")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False


def start_core_chat_engine():
    """Start the Core Chat Engine."""
    print("\n🚀 Starting Core Chat Engine...")
    
    try:
        # Change to core_chat_engine directory
        os.chdir("core_chat_engine")
        
        # Start the FastAPI server
        cmd = [
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
        
        print("🌐 Core Chat Engine will be available at: http://localhost:8000")
        print("📚 API Documentation: http://localhost:8000/docs")
        print("🔍 Health Check: http://localhost:8000/health")
        print("\n💡 Press Ctrl+C to stop the server")
        
        # Run the server
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 Shutting down Core Chat Engine...")
    except Exception as e:
        print(f"❌ Error starting Core Chat Engine: {e}")


def print_usage_info():
    """Print usage information for testing the API."""
    print("\n" + "="*60)
    print("🎯 MANDY AI COMPANION - DEVELOPMENT MODE")
    print("="*60)
    print("\n📋 Quick Start Guide:")
    print("\n1. 🔐 Login to get an access token:")
    print("   POST http://localhost:8000/auth/login")
    print("   Body: {\"username\": \"admin\", \"password\": \"admin123\"}")
    
    print("\n2. 💬 Send a chat message:")
    print("   POST http://localhost:8000/chat/send")
    print("   Headers: Authorization: Bearer <your_token>")
    print("   Body: {\"message\": \"Hello Mandy, how are you?\"}")
    
    print("\n3. 📊 Check health status:")
    print("   GET http://localhost:8000/health")
    
    print("\n4. 🧠 View memory stats:")
    print("   GET http://localhost:8000/memory/stats")
    print("   Headers: Authorization: Bearer <your_token>")
    
    print("\n🔧 Useful URLs:")
    print("   • Core API Docs: http://localhost:8000/docs")
    print("   • Neo4j Browser: http://localhost:7474")
    print("   • Health Check: http://localhost:8000/health")
    
    print("\n📝 Default Credentials:")
    print("   • Username: admin")
    print("   • Password: admin123")
    
    print("\n🐳 Docker Commands:")
    print("   • Stop Neo4j: docker stop mandy-neo4j-dev")
    print("   • Remove Neo4j: docker rm mandy-neo4j-dev")
    
    print("\n" + "="*60)


def main():
    """Main function to start the development environment."""
    print("🤖 Welcome to Mandy AI Companion Development Environment!")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please fix the issues above.")
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Failed to install dependencies.")
        sys.exit(1)
    
    # Start Neo4j
    if not start_neo4j():
        print("\n⚠️  Neo4j failed to start. You may need to start it manually.")
        print("   Manual command: docker run -d --name mandy-neo4j-dev -p 7474:7474 -p 7687:7687 -e NEO4J_AUTH=neo4j/password neo4j:5.26")
    
    # Print usage information
    print_usage_info()
    
    # Start the Core Chat Engine
    start_core_chat_engine()


if __name__ == "__main__":
    main()
