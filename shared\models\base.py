"""
Base data models for Mandy AI Companion.

This module defines the core data structures used throughout the system,
including user models, conversation models, and knowledge graph entities.
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field, validator


class TimestampMixin(BaseModel):
    """Mixin for models that need timestamp tracking."""
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()


class UserRole(str, Enum):
    """User roles in the system."""
    
    USER = "user"
    ADMIN = "admin"
    MODERATOR = "moderator"


class User(TimestampMixin):
    """User model for authentication and personalization."""
    
    id: UUID = Field(default_factory=uuid4)
    username: str = Field(..., min_length=3, max_length=50)
    email: Optional[str] = Field(None, regex=r'^[^@]+@[^@]+\.[^@]+$')
    role: UserRole = Field(default=UserRole.USER)
    is_active: bool = Field(default=True)
    preferences: Dict[str, Any] = Field(default_factory=dict)
    
    # Mandy-specific fields
    group_id: str = Field(..., description="Unique identifier for user's memory group")
    trust_score: float = Field(default=0.5, ge=0.0, le=1.0)
    interaction_count: int = Field(default=0, ge=0)
    last_interaction: Optional[datetime] = None
    
    @validator('username')
    def validate_username(cls, v):
        """Validate username format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username must contain only letters, numbers, hyphens, and underscores')
        return v.lower()


class MessageType(str, Enum):
    """Types of messages in conversations."""
    
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    FUNCTION = "function"


class EmotionType(str, Enum):
    """Emotion types for emotional analysis."""
    
    JOY = "joy"
    SADNESS = "sadness"
    ANGER = "anger"
    FEAR = "fear"
    SURPRISE = "surprise"
    DISGUST = "disgust"
    TRUST = "trust"
    ANTICIPATION = "anticipation"
    NEUTRAL = "neutral"


class Emotion(BaseModel):
    """Emotion detection result."""
    
    type: EmotionType
    intensity: float = Field(..., ge=0.0, le=1.0)
    confidence: float = Field(..., ge=0.0, le=1.0)


class Message(TimestampMixin):
    """Individual message in a conversation."""
    
    id: UUID = Field(default_factory=uuid4)
    conversation_id: UUID
    user_id: UUID
    type: MessageType
    content: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Mandy-specific fields
    emotions: List[Emotion] = Field(default_factory=list)
    typing_delay: Optional[float] = None  # Simulated typing delay in seconds
    burst_index: Optional[int] = None  # Index in multi-message burst
    total_burst_count: Optional[int] = None  # Total messages in burst


class ConversationStatus(str, Enum):
    """Status of conversations."""
    
    ACTIVE = "active"
    PAUSED = "paused"
    ARCHIVED = "archived"
    DELETED = "deleted"


class Conversation(TimestampMixin):
    """Conversation model."""
    
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    title: Optional[str] = None
    status: ConversationStatus = Field(default=ConversationStatus.ACTIVE)
    message_count: int = Field(default=0, ge=0)
    last_message_at: Optional[datetime] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    # Mandy-specific fields
    emotional_context: List[Emotion] = Field(default_factory=list)
    trust_evolution: List[float] = Field(default_factory=list)
    topics_discussed: List[str] = Field(default_factory=list)


class KnowledgeNodeType(str, Enum):
    """Types of nodes in the knowledge graph."""
    
    USER = "User"
    AGENT = "Agent"
    CONCEPT = "Concept"
    EMOTION = "Emotion"
    MEMORY = "Memory"
    DOCUMENT = "Document"
    TOPIC = "Topic"
    EVENT = "Event"


class KnowledgeEdgeType(str, Enum):
    """Types of edges in the knowledge graph."""
    
    INTERACTED_WITH = "INTERACTED_WITH"
    EXPRESSED = "EXPRESSED"
    HAS_INTEREST_IN = "HAS_INTEREST_IN"
    TRUSTS = "TRUSTS"
    DISCUSSED = "DISCUSSED"
    REFERENCES = "REFERENCES"
    RELATES_TO = "RELATES_TO"
    OCCURRED_AT = "OCCURRED_AT"


class KnowledgeNode(TimestampMixin):
    """Node in the knowledge graph."""
    
    id: UUID = Field(default_factory=uuid4)
    type: KnowledgeNodeType
    name: str
    properties: Dict[str, Any] = Field(default_factory=dict)
    group_id: Optional[str] = None  # For user-specific vs global knowledge
    
    # Temporal properties
    valid_from: datetime = Field(default_factory=datetime.utcnow)
    valid_to: Optional[datetime] = None


class KnowledgeEdge(TimestampMixin):
    """Edge in the knowledge graph."""
    
    id: UUID = Field(default_factory=uuid4)
    type: KnowledgeEdgeType
    source_id: UUID
    target_id: UUID
    properties: Dict[str, Any] = Field(default_factory=dict)
    weight: float = Field(default=1.0, ge=0.0, le=1.0)
    
    # Temporal properties
    valid_from: datetime = Field(default_factory=datetime.utcnow)
    valid_to: Optional[datetime] = None


class Episode(TimestampMixin):
    """Episode for Graphiti ingestion."""
    
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    group_id: str
    content: Union[str, Dict[str, Any]]
    episode_type: str = Field(default="conversation")
    source: str = Field(default="chat")
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SearchQuery(BaseModel):
    """Search query for knowledge retrieval."""
    
    query: str
    user_id: UUID
    group_id: str
    limit: int = Field(default=10, ge=1, le=100)
    filters: Dict[str, Any] = Field(default_factory=dict)
    search_type: str = Field(default="hybrid")  # semantic, keyword, graph, hybrid


class SearchResult(BaseModel):
    """Search result from knowledge retrieval."""
    
    id: UUID
    content: str
    score: float = Field(..., ge=0.0, le=1.0)
    type: str
    metadata: Dict[str, Any] = Field(default_factory=dict)
    timestamp: datetime


class HealthStatus(BaseModel):
    """Health check status."""
    
    service: str
    status: str  # healthy, unhealthy, degraded
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    details: Dict[str, Any] = Field(default_factory=dict)
    dependencies: Dict[str, str] = Field(default_factory=dict)


class APIResponse(BaseModel):
    """Standard API response format."""
    
    success: bool
    message: str
    data: Optional[Any] = None
    errors: Optional[List[str]] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    request_id: Optional[str] = None
