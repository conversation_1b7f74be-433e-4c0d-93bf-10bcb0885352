"""
Configuration management for Mandy AI Companion.

This module provides centralized configuration loading and validation
with environment-specific overrides and secure secret management.
"""

import os
from typing import Any, Dict, List, Optional, Union
from pathlib import Path

from pydantic import BaseSettings, Field, validator


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    
    neo4j_uri: str = Field(..., env="NEO4J_URI")
    neo4j_user: str = Field(..., env="NEO4J_USER")
    neo4j_password: str = Field(..., env="NEO4J_PASSWORD")
    neo4j_database: str = Field("neo4j", env="NEO4J_DATABASE")
    
    # Connection pool settings
    max_connection_lifetime: int = Field(3600, env="NEO4J_MAX_CONNECTION_LIFETIME")
    max_connection_pool_size: int = Field(50, env="NEO4J_MAX_CONNECTION_POOL_SIZE")
    connection_acquisition_timeout: int = Field(60, env="NEO4J_CONNECTION_TIMEOUT")


class GeminiConfig(BaseSettings):
    """Google Gemini API configuration."""
    
    api_key: str = Field(..., env="GEMINI_API_KEY")
    model: str = Field("gemini-1.5-pro", env="GEMINI_MODEL")
    small_model: str = Field("gemini-1.5-flash", env="GEMINI_SMALL_MODEL")
    max_tokens: int = Field(8192, env="GEMINI_MAX_TOKENS")
    temperature: float = Field(0.7, env="GEMINI_TEMPERATURE")
    
    @validator('temperature')
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError('Temperature must be between 0.0 and 2.0')
        return v


class GraphitiConfig(BaseSettings):
    """Graphiti framework configuration."""
    
    embedding_model: str = Field("text-embedding-004", env="GRAPHITI_EMBEDDING_MODEL")
    embedding_dimension: int = Field(768, env="GRAPHITI_EMBEDDING_DIMENSION")
    max_context_length: int = Field(4000, env="GRAPHITI_MAX_CONTEXT_LENGTH")
    
    # Search configuration
    default_search_limit: int = Field(10, env="GRAPHITI_DEFAULT_SEARCH_LIMIT")
    max_search_limit: int = Field(100, env="GRAPHITI_MAX_SEARCH_LIMIT")


class SecurityConfig(BaseSettings):
    """Security and authentication configuration."""
    
    secret_key: str = Field(..., env="SECRET_KEY")
    access_token_expire_minutes: int = Field(60, env="ACCESS_TOKEN_EXPIRE_MINUTES")
    refresh_token_expire_days: int = Field(7, env="REFRESH_TOKEN_EXPIRE_DAYS")
    
    # CORS settings
    cors_origins: List[str] = Field(
        ["http://localhost:7860", "http://localhost:3000"],
        env="CORS_ORIGINS"
    )
    cors_allow_credentials: bool = Field(True, env="CORS_ALLOW_CREDENTIALS")
    
    # Rate limiting
    rate_limit_requests_per_minute: int = Field(60, env="RATE_LIMIT_REQUESTS_PER_MINUTE")
    rate_limit_burst: int = Field(10, env="RATE_LIMIT_BURST")
    
    @validator('cors_origins', pre=True)
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            # Handle JSON string format
            import json
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # Handle comma-separated string
                return [origin.strip() for origin in v.split(',')]
        return v


class ExternalAPIConfig(BaseSettings):
    """External API configuration."""
    
    # News API
    news_api_key: Optional[str] = Field(None, env="NEWS_API_KEY")
    
    # Reddit API
    reddit_client_id: Optional[str] = Field(None, env="REDDIT_CLIENT_ID")
    reddit_client_secret: Optional[str] = Field(None, env="REDDIT_CLIENT_SECRET")
    reddit_user_agent: str = Field("Mandy/1.0", env="REDDIT_USER_AGENT")
    
    # arXiv API
    arxiv_api_base_url: str = Field(
        "http://export.arxiv.org/api/query",
        env="ARXIV_API_BASE_URL"
    )


class WorkerConfig(BaseSettings):
    """Background worker configuration."""
    
    # Content fetcher settings
    content_fetch_interval: int = Field(3600, env="CONTENT_FETCH_INTERVAL")  # seconds
    max_articles_per_fetch: int = Field(10, env="MAX_ARTICLES_PER_FETCH")
    content_retention_days: int = Field(30, env="CONTENT_RETENTION_DAYS")
    
    # Reflection engine settings
    reflection_interval: int = Field(1800, env="REFLECTION_INTERVAL")  # seconds
    trust_score_decay_rate: float = Field(0.01, env="TRUST_SCORE_DECAY_RATE")
    emotion_analysis_threshold: float = Field(0.7, env="EMOTION_ANALYSIS_THRESHOLD")
    
    # Worker pool settings
    max_worker_threads: int = Field(4, env="MAX_WORKER_THREADS")
    worker_queue_size: int = Field(100, env="WORKER_QUEUE_SIZE")


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_format: str = Field("json", env="LOG_FORMAT")
    log_file: Optional[str] = Field(None, env="LOG_FILE")
    
    # Feature flags for logging
    enable_request_logging: bool = Field(True, env="ENABLE_REQUEST_LOGGING")
    enable_performance_logging: bool = Field(True, env="ENABLE_PERFORMANCE_LOGGING")
    enable_security_logging: bool = Field(True, env="ENABLE_SECURITY_LOGGING")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @validator('log_format')
    def validate_log_format(cls, v):
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f'Log format must be one of: {valid_formats}')
        return v.lower()


class ServiceConfig(BaseSettings):
    """Service-specific configuration."""
    
    # Environment
    environment: str = Field("development", env="ENVIRONMENT")
    debug: bool = Field(False, env="DEBUG")
    
    # Service URLs
    core_api_url: str = Field("http://localhost:8000", env="CORE_API_URL")
    ui_url: str = Field("http://localhost:7860", env="UI_URL")
    
    # Service ports
    core_api_port: int = Field(8000, env="CORE_API_PORT")
    ui_port: int = Field(7860, env="UI_PORT")
    
    # Health check settings
    health_check_interval: int = Field(30, env="HEALTH_CHECK_INTERVAL")
    health_check_timeout: int = Field(10, env="HEALTH_CHECK_TIMEOUT")
    health_check_retries: int = Field(3, env="HEALTH_CHECK_RETRIES")
    
    @validator('environment')
    def validate_environment(cls, v):
        valid_envs = ["development", "staging", "production"]
        if v.lower() not in valid_envs:
            raise ValueError(f'Environment must be one of: {valid_envs}')
        return v.lower()


class FeatureFlags(BaseSettings):
    """Feature flags for enabling/disabling functionality."""
    
    enable_content_fetching: bool = Field(True, env="ENABLE_CONTENT_FETCHING")
    enable_reflection_engine: bool = Field(True, env="ENABLE_REFLECTION_ENGINE")
    enable_emotion_analysis: bool = Field(True, env="ENABLE_EMOTION_ANALYSIS")
    enable_trust_modeling: bool = Field(True, env="ENABLE_TRUST_MODELING")
    
    # Experimental features
    enable_voice_synthesis: bool = Field(False, env="ENABLE_VOICE_SYNTHESIS")
    enable_image_analysis: bool = Field(False, env="ENABLE_IMAGE_ANALYSIS")
    enable_multimodal_chat: bool = Field(False, env="ENABLE_MULTIMODAL_CHAT")


class MandyConfig(BaseSettings):
    """Main configuration class that combines all settings."""
    
    # Sub-configurations
    database: DatabaseConfig = DatabaseConfig()
    gemini: GeminiConfig = GeminiConfig()
    graphiti: GraphitiConfig = GraphitiConfig()
    security: SecurityConfig = SecurityConfig()
    external_apis: ExternalAPIConfig = ExternalAPIConfig()
    workers: WorkerConfig = WorkerConfig()
    logging: LoggingConfig = LoggingConfig()
    service: ServiceConfig = ServiceConfig()
    features: FeatureFlags = FeatureFlags()
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


def load_config(env_file: Optional[str] = None) -> MandyConfig:
    """
    Load configuration from environment variables and .env file.
    
    Args:
        env_file: Path to .env file (optional)
    
    Returns:
        Loaded configuration object
    """
    if env_file and Path(env_file).exists():
        return MandyConfig(_env_file=env_file)
    
    # Try to find .env file in current directory or parent directories
    current_dir = Path.cwd()
    for path in [current_dir] + list(current_dir.parents):
        env_path = path / ".env"
        if env_path.exists():
            return MandyConfig(_env_file=str(env_path))
    
    # Load from environment variables only
    return MandyConfig()


def get_config() -> MandyConfig:
    """Get the global configuration instance."""
    if not hasattr(get_config, '_config'):
        get_config._config = load_config()
    return get_config._config


def reload_config(env_file: Optional[str] = None) -> MandyConfig:
    """Reload configuration (useful for testing)."""
    if hasattr(get_config, '_config'):
        delattr(get_config, '_config')
    return load_config(env_file)
