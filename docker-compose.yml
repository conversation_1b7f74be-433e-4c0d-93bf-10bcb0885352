version: '3.8'

services:
  neo4j:
    image: neo4j:latest
    container_name: mandy-neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/your_neo4j_password_here
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - mandy-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "your_neo4j_password_here", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  core-chat-engine:
    build:
      context: ./core_chat_engine
      dockerfile: Dockerfile
    container_name: mandy-core-chat-engine
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=INFO
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=your_neo4j_password_here
      - NEO4J_DATABASE=neo4j
    env_file:
      - .env
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    volumes:
      - ./core_chat_engine:/app
    restart: unless-stopped

  ui:
    build:
      context: ./ui
      dockerfile: Dockerfile
    container_name: mandy-ui
    ports:
      - "7860:7860"
    environment:
      - UI_HOST=0.0.0.0
      - UI_PORT=7860
      - CORE_CHAT_ENGINE_URL=http://core-chat-engine:8000
    env_file:
      - .env
    depends_on:
      - core-chat-engine
    networks:
      - mandy-network
    volumes:
      - ./ui:/app
    restart: unless-stopped

  workers:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: mandy-workers
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=your_neo4j_password_here
    env_file:
      - .env
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    volumes:
      - ./workers:/app
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

networks:
  mandy-network:
    driver: bridge
version: '3.8'

services:
  neo4j:
    image: neo4j:latest
    container_name: mandy-neo4j
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      - NEO4J_AUTH=neo4j/your_neo4j_password_here
      - NEO4J_PLUGINS=["apoc"]
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_security_procedures_allowlist=apoc.*
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - mandy-network
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "neo4j", "-p", "your_neo4j_password_here", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  core-chat-engine:
    build:
      context: ./core_chat_engine
      dockerfile: Dockerfile
    container_name: mandy-core-chat-engine
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - LOG_LEVEL=INFO
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=your_neo4j_password_here
      - NEO4J_DATABASE=neo4j
    env_file:
      - .env
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    volumes:
      - ./core_chat_engine:/app
    restart: unless-stopped

  ui:
    build:
      context: ./ui
      dockerfile: Dockerfile
    container_name: mandy-ui
    ports:
      - "7860:7860"
    environment:
      - UI_HOST=0.0.0.0
      - UI_PORT=7860
      - CORE_CHAT_ENGINE_URL=http://core-chat-engine:8000
    env_file:
      - .env
    depends_on:
      - core-chat-engine
    networks:
      - mandy-network
    volumes:
      - ./ui:/app
    restart: unless-stopped

  workers:
    build:
      context: ./workers
      dockerfile: Dockerfile
    container_name: mandy-workers
    environment:
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USERNAME=neo4j
      - NEO4J_PASSWORD=your_neo4j_password_here
    env_file:
      - .env
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    volumes:
      - ./workers:/app
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:

networks:
  mandy-network:
    driver: bridge
