"""
Gradio UI for Mandy AI Companion

This module provides a web-based chat interface for interacting with <PERSON>,
including authentication, real-time chat, and typing indicators.
"""

import asyncio
import os
import time
from typing import List, <PERSON><PERSON>, Optional

import gradio as gr
import httpx
from loguru import logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
CORE_CHAT_ENGINE_URL = os.getenv("CORE_CHAT_ENGINE_URL", "http://localhost:8000")
DEMO_USERNAME = os.getenv("DEMO_USERNAME", "user")
DEMO_PASSWORD = os.getenv("DEMO_PASSWORD", "password")

# Global state
current_user_token = None
current_user_info = None


class MandyUI:
    """Main UI class for Mandy AI Companion."""

    def __init__(self):
        """Initialize the Mandy UI."""
        self.client = httpx.AsyncClient(timeout=30.0)
        self.conversation_id = None

    async def authenticate(self, username: str, password: str) -> <PERSON><PERSON>[bool, str]:
        """
        Authenticate user with the core chat engine.

        Args:
            username: <PERSON>rna<PERSON> for authentication
            password: Password for authentication

        Returns:
            Tuple of (success, message)
        """
        try:
            response = await self.client.post(
                f"{CORE_CHAT_ENGINE_URL}/auth/login",
                json={"username": username, "password": password}
            )

            if response.status_code == 200:
                data = response.json()
                global current_user_token, current_user_info
                current_user_token = data["access_token"]
                current_user_info = data["user"]

                logger.info(f"User authenticated: {username}")
                return True, f"Welcome, {username}! You're now connected to Mandy."
            else:
                error_msg = response.json().get("detail", "Authentication failed")
                logger.warning(f"Authentication failed for {username}: {error_msg}")
                return False, f"Authentication failed: {error_msg}"

        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return False, f"Connection error: {str(e)}"

    async def send_message(self, message: str, history: List[List[str]]) -> Tuple[List[List[str]], str]:
        """
        Send a message to Mandy and get response.

        Args:
            message: User's message
            history: Chat history

        Returns:
            Tuple of (updated_history, empty_string_for_input)
        """
        if not current_user_token:
            history.append([message, "Please log in first to chat with Mandy."])
            return history, ""

        try:
            # Add user message to history
            history.append([message, ""])

            # Send request to core chat engine
            headers = {"X-User-ID": current_user_info.get("user_id", "demo_user")}

            response = await self.client.post(
                f"{CORE_CHAT_ENGINE_URL}/chat/",
                json={
                    "message": message,
                    "user_id": current_user_info.get("user_id", "demo_user"),
                    "conversation_id": self.conversation_id,
                    "use_grounding": False
                },
                headers=headers
            )

            if response.status_code == 200:
                data = response.json()

                # Update conversation ID
                self.conversation_id = data.get("conversation_id")

                # Get response text
                response_text = data.get("response_text", "I'm having trouble responding right now.")

                # Get message bursts for typing simulation
                message_bursts = data.get("message_bursts", [])

                # Update the last message in history with Mandy's response
                history[-1][1] = response_text

                logger.info(f"Message processed successfully. Response length: {len(response_text)}")

            else:
                error_msg = response.json().get("detail", "Failed to process message")
                history[-1][1] = f"Sorry, I'm having trouble right now: {error_msg}"
                logger.error(f"Chat API error: {error_msg}")

        except Exception as e:
            history[-1][1] = f"I'm having connection issues: {str(e)}"
            logger.error(f"Chat error: {str(e)}")

        return history, ""

    def create_interface(self) -> gr.Blocks:
        """
        Create the Gradio interface.

        Returns:
            Gradio Blocks interface
        """
        with gr.Blocks(
            title="Mandy AI Companion",
            theme=gr.themes.Soft(),
            css="""
            .gradio-container {
                max-width: 800px !important;
                margin: auto !important;
            }
            .chat-message {
                padding: 10px;
                margin: 5px 0;
                border-radius: 10px;
            }
            .user-message {
                background-color: #e3f2fd;
                text-align: right;
            }
            .bot-message {
                background-color: #f5f5f5;
                text-align: left;
            }
            """
        ) as interface:

            gr.Markdown(
                """
                # 💝 Mandy AI Companion

                Welcome to Mandy, your emotionally intelligent AI companion.
                Mandy remembers your conversations and builds a genuine connection with you over time.

                **Demo Credentials:**
                - Username: `user`
                - Password: `password`
                """
            )

            with gr.Row():
                with gr.Column(scale=1):
                    # Authentication section
                    with gr.Group():
                        gr.Markdown("### 🔐 Login")
                        username_input = gr.Textbox(
                            label="Username",
                            placeholder="Enter your username",
                            value=DEMO_USERNAME
                        )
                        password_input = gr.Textbox(
                            label="Password",
                            placeholder="Enter your password",
                            type="password",
                            value=DEMO_PASSWORD
                        )
                        login_btn = gr.Button("Login", variant="primary")
                        auth_status = gr.Textbox(
                            label="Status",
                            interactive=False,
                            value="Please log in to start chatting with Mandy"
                        )

                with gr.Column(scale=2):
                    # Chat section
                    with gr.Group():
                        gr.Markdown("### 💬 Chat with Mandy")
                        chatbot = gr.Chatbot(
                            label="Conversation",
                            height=400,
                            show_label=False,
                            avatar_images=("👤", "🤖")
                        )

                        with gr.Row():
                            message_input = gr.Textbox(
                                label="Message",
                                placeholder="Type your message to Mandy...",
                                scale=4,
                                show_label=False
                            )
                            send_btn = gr.Button("Send", variant="primary", scale=1)

                        gr.Markdown(
                            """
                            **Tips:**
                            - Mandy remembers your conversations and builds context over time
                            - She responds with emotional intelligence and empathy
                            - Try sharing your thoughts, feelings, or asking for advice
                            - The more you chat, the better Mandy gets to know you
                            """
                        )

            # Event handlers
            async def handle_login(username: str, password: str):
                """Handle login button click."""
                success, message = await self.authenticate(username, password)
                if success:
                    return gr.update(value=message, label="✅ Status")
                else:
                    return gr.update(value=message, label="❌ Status")

            async def handle_message(message: str, history: List[List[str]]):
                """Handle message sending."""
                if not message.strip():
                    return history, ""

                return await self.send_message(message, history)

            # Wire up events
            login_btn.click(
                fn=handle_login,
                inputs=[username_input, password_input],
                outputs=[auth_status]
            )

            send_btn.click(
                fn=handle_message,
                inputs=[message_input, chatbot],
                outputs=[chatbot, message_input]
            )

            message_input.submit(
                fn=handle_message,
                inputs=[message_input, chatbot],
                outputs=[chatbot, message_input]
            )

        return interface


async def main():
    """Main function to run the Gradio app."""
    logger.info("Starting Mandy UI...")

    # Create UI instance
    ui = MandyUI()

    # Create interface
    interface = ui.create_interface()

    # Get configuration
    host = os.getenv("UI_HOST", "0.0.0.0")
    port = int(os.getenv("UI_PORT", "7860"))

    logger.info(f"Launching Gradio interface on {host}:{port}")

    # Launch interface
    interface.launch(
        server_name=host,
        server_port=port,
        share=False,
        show_error=True,
        quiet=False
    )


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
