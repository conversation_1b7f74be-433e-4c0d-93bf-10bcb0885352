version: '3.8'

services:
  # Neo4j Database - Production Configuration
  neo4j:
    image: neo4j:5.26
    container_name: mandy-neo4j-prod
    ports:
      - "7687:7687"  # Only Bolt for production
    environment:
      - NEO4J_AUTH=${NEO4J_USER}/${NEO4J_PASSWORD}
      - NEO4J_dbms_memory_heap_initial__size=512m
      - NEO4J_dbms_memory_heap_max__size=2G
      - NEO4J_dbms_memory_pagecache_size=1G
      - NEO4J_dbms_security_procedures_unrestricted=apoc.*
      - NEO4J_dbms_logs_query_enabled=false
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    networks:
      - mandy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "cypher-shell", "-u", "${NEO4J_USER}", "-p", "${NEO4J_PASSWORD}", "RETURN 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 3G
        reservations:
          memory: 1G

  # Core Chat Engine - Production Configuration
  core_chat_engine:
    build:
      context: ./core_chat_engine
      dockerfile: Dockerfile.prod
    container_name: mandy-core-prod
    ports:
      - "${CORE_API_PORT:-8000}:8000"
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=${NEO4J_URI}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - LOG_LEVEL=${LOG_LEVEL:-WARNING}
      - DEBUG=false
      - RELOAD=false
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - mandy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M

  # Content Fetcher Worker - Production Configuration
  content_fetcher:
    build:
      context: ./workers
      dockerfile: Dockerfile.prod
    container_name: mandy-content-fetcher-prod
    command: python -m content_fetcher.main
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=${NEO4J_URI}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - CORE_API_URL=http://core_chat_engine:8000
      - NEWS_API_KEY=${NEWS_API_KEY}
      - REDDIT_CLIENT_ID=${REDDIT_CLIENT_ID}
      - REDDIT_CLIENT_SECRET=${REDDIT_CLIENT_SECRET}
      - CONTENT_FETCH_INTERVAL=${CONTENT_FETCH_INTERVAL:-3600}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=production
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Reflection Engine Worker - Production Configuration
  reflection_engine:
    build:
      context: ./workers
      dockerfile: Dockerfile.prod
    container_name: mandy-reflection-prod
    command: python -m reflection_engine.main
    environment:
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - NEO4J_URI=${NEO4J_URI}
      - NEO4J_USER=${NEO4J_USER}
      - NEO4J_PASSWORD=${NEO4J_PASSWORD}
      - CORE_API_URL=http://core_chat_engine:8000
      - REFLECTION_INTERVAL=${REFLECTION_INTERVAL:-1800}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=production
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Gradio Web UI - Production Configuration
  ui:
    build:
      context: ./ui
      dockerfile: Dockerfile.prod
    container_name: mandy-ui-prod
    ports:
      - "${UI_PORT:-7860}:7860"
    environment:
      - CORE_API_URL=http://core_chat_engine:8000
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=false
    depends_on:
      core_chat_engine:
        condition: service_healthy
    networks:
      - mandy-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: mandy-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - core_chat_engine
      - ui
    networks:
      - mandy-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

volumes:
  neo4j_data:
    driver: local
  neo4j_logs:
    driver: local

networks:
  mandy-network:
    driver: bridge
