# Render.com deployment configuration for Mandy AI Companion
# This configuration deploys all services to <PERSON><PERSON>'s free tier

services:
  # Core Chat Engine API
  - type: web
    name: mandy-core-api
    env: docker
    dockerfilePath: ./core_chat_engine/Dockerfile.prod
    plan: free
    region: oregon
    branch: main
    healthCheckPath: /health
    envVars:
      - key: GEMINI_API_KEY
        sync: false
      - key: SECRET_KEY
        generateValue: true
      - key: NEO4J_URI
        fromService:
          type: pserv
          name: mandy-neo4j
          property: connectionString
      - key: NEO4J_USER
        value: neo4j
      - key: NEO4J_PASSWORD
        fromService:
          type: pserv
          name: mandy-neo4j
          property: password
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO
      - key: CORS_ORIGINS
        value: '["https://mandy-ui.onrender.com"]'

  # Gradio Web UI
  - type: web
    name: mandy-ui
    env: docker
    dockerfilePath: ./ui/Dockerfile.prod
    plan: free
    region: oregon
    branch: main
    envVars:
      - key: CORE_API_URL
        fromService:
          type: web
          name: mandy-core-api
          property: host
      - key: SECRET_KEY
        fromService:
          type: web
          name: mandy-core-api
          envVarKey: SECRET_KEY
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO

  # Content Fetcher Worker
  - type: worker
    name: mandy-content-fetcher
    env: docker
    dockerfilePath: ./workers/Dockerfile.prod
    dockerCommand: python -m content_fetcher.main
    plan: free
    region: oregon
    branch: main
    envVars:
      - key: GEMINI_API_KEY
        sync: false
      - key: NEO4J_URI
        fromService:
          type: pserv
          name: mandy-neo4j
          property: connectionString
      - key: NEO4J_USER
        value: neo4j
      - key: NEO4J_PASSWORD
        fromService:
          type: pserv
          name: mandy-neo4j
          property: password
      - key: CORE_API_URL
        fromService:
          type: web
          name: mandy-core-api
          property: host
      - key: NEWS_API_KEY
        sync: false
      - key: REDDIT_CLIENT_ID
        sync: false
      - key: REDDIT_CLIENT_SECRET
        sync: false
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO

  # Reflection Engine Worker
  - type: worker
    name: mandy-reflection-engine
    env: docker
    dockerfilePath: ./workers/Dockerfile.prod
    dockerCommand: python -m reflection_engine.main
    plan: free
    region: oregon
    branch: main
    envVars:
      - key: GEMINI_API_KEY
        sync: false
      - key: NEO4J_URI
        fromService:
          type: pserv
          name: mandy-neo4j
          property: connectionString
      - key: NEO4J_USER
        value: neo4j
      - key: NEO4J_PASSWORD
        fromService:
          type: pserv
          name: mandy-neo4j
          property: password
      - key: CORE_API_URL
        fromService:
          type: web
          name: mandy-core-api
          property: host
      - key: ENVIRONMENT
        value: production
      - key: LOG_LEVEL
        value: INFO

databases:
  # Neo4j Database (using PostgreSQL as a placeholder - Neo4j not directly supported)
  # Note: For production, consider using Neo4j AuraDB cloud service
  - name: mandy-neo4j
    databaseName: mandy
    user: neo4j
    plan: free
    region: oregon

# Note: Render doesn't natively support Neo4j, so you'll need to:
# 1. Use Neo4j AuraDB (cloud service) and update NEO4J_URI accordingly
# 2. Or deploy Neo4j as a private service (paid plan required)
# 3. For free tier testing, consider using a different graph database or 
#    modify the configuration to use PostgreSQL with graph extensions
