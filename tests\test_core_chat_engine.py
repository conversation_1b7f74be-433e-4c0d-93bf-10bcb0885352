"""
Tests for the Core Chat Engine.

This module contains tests for the main functionality of <PERSON>'s Core Chat Engine,
including authentication, chat processing, and memory operations.
"""

import asyncio
import json
import os
import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import UUID, uuid4

import httpx
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.testclient import TestClient

from shared.models.base import User, UserRole, Emotion, EmotionType, Episode
from shared.auth.jwt_handler import J<PERSON><PERSON><PERSON><PERSON>

# Import the FastAPI app
from core_chat_engine.app.main import app
from core_chat_engine.app.services.user_service import UserService
from core_chat_engine.app.services.gemini_service import GeminiService
from core_chat_engine.app.services.graphiti_service import GraphitiService
from core_chat_engine.app.services.emotion_service import EmotionService
from core_chat_engine.app.services.typing_engine import TypingEngine


# Test client
client = TestClient(app)


# Mock user for testing
@pytest.fixture
def test_user():
    """Create a test user for authentication."""
    return User(
        id=UUID("00000000-0000-0000-0000-000000000001"),
        username="testuser",
        email="<EMAIL>",
        role=UserRole.USER,
        group_id="test_group",
        trust_score=0.5,
        interaction_count=5,
        is_active=True
    )


# Mock JWT handler
@pytest.fixture
def jwt_handler():
    """Create a JWT handler with test secret."""
    os.environ["SECRET_KEY"] = "test_secret_key"
    return JWTHandler()


# Mock services
@pytest.fixture
def mock_user_service(test_user):
    """Mock user service for authentication."""
    service = MagicMock(spec=UserService)
    
    # Mock authentication
    async def authenticate_mock(username, password):
        if username == "testuser" and password == "password":
            return test_user
        return None
    
    # Mock get user by ID
    async def get_user_by_id_mock(user_id):
        if str(user_id) == str(test_user.id):
            return test_user
        return None
    
    service.authenticate_user = AsyncMock(side_effect=authenticate_mock)
    service.get_user_by_id = AsyncMock(side_effect=get_user_by_id_mock)
    service.update_last_interaction = AsyncMock(return_value=None)
    
    return service


@pytest.fixture
def mock_gemini_service():
    """Mock Gemini service for response generation."""
    service = MagicMock(spec=GeminiService)
    
    async def generate_response_mock(context, user_id, use_small_model=False):
        return "This is a test response from Gemini. I'm Mandy, your AI companion. How are you feeling today?"
    
    service.generate_response = AsyncMock(side_effect=generate_response_mock)
    
    return service


@pytest.fixture
def mock_graphiti_service():
    """Mock Graphiti service for memory operations."""
    service = MagicMock(spec=GraphitiService)
    
    async def search_relevant_context_mock(query, user_id, group_id, limit=10):
        return [
            {
                "content": "User previously mentioned they enjoy hiking.",
                "score": 0.85,
                "timestamp": datetime.utcnow() - timedelta(days=3),
                "source": "conversation"
            },
            {
                "content": "User's favorite color is blue.",
                "score": 0.75,
                "timestamp": datetime.utcnow() - timedelta(days=7),
                "source": "conversation"
            }
        ]
    
    async def add_episode_mock(episode):
        return episode.id
    
    service.search_relevant_context = AsyncMock(side_effect=search_relevant_context_mock)
    service.add_episode = AsyncMock(side_effect=add_episode_mock)
    service.update_user_metrics = AsyncMock(return_value=None)
    
    return service


@pytest.fixture
def mock_emotion_service():
    """Mock emotion service for sentiment analysis."""
    service = MagicMock(spec=EmotionService)
    
    async def analyze_emotions_mock(text):
        if "happy" in text.lower():
            return [
                Emotion(type=EmotionType.JOY, intensity=0.8, confidence=0.9),
                Emotion(type=EmotionType.ANTICIPATION, intensity=0.5, confidence=0.7)
            ]
        elif "sad" in text.lower():
            return [
                Emotion(type=EmotionType.SADNESS, intensity=0.7, confidence=0.8),
                Emotion(type=EmotionType.FEAR, intensity=0.3, confidence=0.6)
            ]
        else:
            return [
                Emotion(type=EmotionType.NEUTRAL, intensity=0.9, confidence=0.8)
            ]
    
    service.analyze_emotions = AsyncMock(side_effect=analyze_emotions_mock)
    
    return service


@pytest.fixture
def mock_typing_engine():
    """Mock typing engine for response formatting."""
    service = MagicMock(spec=TypingEngine)
    
    async def process_response_mock(response, user, context):
        # Split response into two messages
        parts = response.split(". ")
        if len(parts) > 1:
            return [
                {
                    "content": parts[0] + ".",
                    "typing_delay": 1.5,
                    "emotions": [],
                    "metadata": {"index": 0, "total": 2}
                },
                {
                    "content": ". ".join(parts[1:]),
                    "typing_delay": 2.0,
                    "emotions": [],
                    "metadata": {"index": 1, "total": 2}
                }
            ]
        else:
            return [
                {
                    "content": response,
                    "typing_delay": 1.0,
                    "emotions": [],
                    "metadata": {"index": 0, "total": 1}
                }
            ]
    
    service.process_response = AsyncMock(side_effect=process_response_mock)
    
    return service


@pytest.fixture
def auth_token(test_user, jwt_handler):
    """Generate a valid authentication token."""
    return jwt_handler.create_access_token(test_user)


# Apply mocks to the app
@pytest.fixture(autouse=True)
def apply_mocks(
    mock_user_service,
    mock_gemini_service,
    mock_graphiti_service,
    mock_emotion_service,
    mock_typing_engine
):
    """Apply all mocks to the FastAPI app."""
    with patch("core_chat_engine.app.routes.auth.get_user_service", return_value=mock_user_service), \
         patch("core_chat_engine.app.routes.chat.get_gemini_service", return_value=mock_gemini_service), \
         patch("core_chat_engine.app.routes.chat.get_graphiti_service", return_value=mock_graphiti_service), \
         patch("core_chat_engine.app.routes.chat.get_emotion_service", return_value=mock_emotion_service), \
         patch("core_chat_engine.app.routes.chat.get_typing_engine", return_value=mock_typing_engine), \
         patch("core_chat_engine.app.routes.memory.get_graphiti_service", return_value=mock_graphiti_service), \
         patch("core_chat_engine.app.routes.history.get_graphiti_service", return_value=mock_graphiti_service):
        yield


# Tests
def test_root_endpoint():
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Mandy Core Chat Engine"
    assert data["status"] == "online"


def test_health_check():
    """Test the health check endpoint."""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["service"] == "core_chat_engine"
    assert data["status"] in ["healthy", "degraded", "unhealthy"]


def test_login_success():
    """Test successful login."""
    response = client.post(
        "/auth/login",
        json={"username": "testuser", "password": "password"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "access_token" in data["data"]
    assert "refresh_token" in data["data"]
    assert data["data"]["user"]["username"] == "testuser"


def test_login_failure():
    """Test failed login."""
    response = client.post(
        "/auth/login",
        json={"username": "testuser", "password": "wrong_password"}
    )
    assert response.status_code == 401


def test_get_current_user(auth_token):
    """Test getting current user information."""
    response = client.get(
        "/auth/me",
        headers={"Authorization": f"Bearer {auth_token}"}
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert data["data"]["username"] == "testuser"
    assert data["data"]["role"] == "user"


def test_send_message(auth_token):
    """Test sending a chat message."""
    response = client.post(
        "/chat/send",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "message": "Hello, I'm feeling happy today!",
            "conversation_id": str(uuid4()),
            "context": {}
        }
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "messages" in data["data"]
    assert len(data["data"]["messages"]) > 0
    assert "user_emotions" in data["data"]
    assert data["data"]["user_emotions"][0]["type"] == "joy"


def test_unauthorized_access():
    """Test accessing protected endpoint without authentication."""
    response = client.post(
        "/chat/send",
        json={"message": "Hello"}
    )
    assert response.status_code == 401


def test_invalid_token():
    """Test accessing protected endpoint with invalid token."""
    response = client.get(
        "/auth/me",
        headers={"Authorization": "Bearer invalid_token"}
    )
    assert response.status_code == 401


# Integration tests
def test_chat_flow(auth_token):
    """Test the complete chat flow."""
    # Send a message
    response = client.post(
        "/chat/send",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "message": "Tell me about yourself, Mandy.",
            "context": {"test": True}
        }
    )
    assert response.status_code == 200
    data = response.json()
    
    # Get conversation ID from response
    conversation_id = data["data"]["conversation_id"]
    
    # Send a follow-up message in the same conversation
    response = client.post(
        "/chat/send",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={
            "message": "I'm feeling sad today.",
            "conversation_id": conversation_id,
            "context": {"test": True}
        }
    )
    assert response.status_code == 200
    data = response.json()
    
    # Verify emotional analysis
    assert data["data"]["user_emotions"][0]["type"] == "sadness"
    
    # Verify conversation ID is maintained
    assert data["data"]["conversation_id"] == conversation_id


# Run tests
if __name__ == "__main__":
    pytest.main(["-xvs", __file__])
