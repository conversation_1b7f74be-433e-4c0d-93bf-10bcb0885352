# Minimal requirements for Mandy Core Chat Engine
# Just the essential dependencies to get the API running

# FastAPI and server
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client
httpx==0.25.2

# Google Gemini AI
google-generativeai==0.3.2

# Graphiti memory framework (install from GitHub)
git+https://github.com/getzep/graphiti.git#egg=graphiti-core&subdirectory=graphiti_core

# Neo4j driver
neo4j==5.15.0

# Authentication
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Logging
loguru==0.7.2

# Environment variables
python-dotenv==1.0.0

# Date utilities
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10
