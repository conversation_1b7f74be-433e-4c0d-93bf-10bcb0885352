{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile"}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "environments": {"production": {"variables": {"ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DEBUG": "false", "PYTHONPATH": "/app"}}}, "services": [{"name": "mandy-core-api", "source": {"repo": "your-repo/mandy", "rootDirectory": "/core_chat_engine"}, "build": {"dockerfilePath": "Dockerfile.prod"}, "variables": {"PORT": "${{ PORT }}", "GEMINI_API_KEY": "${{ GEMINI_API_KEY }}", "SECRET_KEY": "${{ SECRET_KEY }}", "NEO4J_URI": "${{ NEO4J_URI }}", "NEO4J_USER": "${{ NEO4J_USER }}", "NEO4J_PASSWORD": "${{ NEO4J_PASSWORD }}", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO"}, "domains": [{"name": "mandy-api.railway.app"}]}, {"name": "mandy-ui", "source": {"repo": "your-repo/mandy", "rootDirectory": "/ui"}, "build": {"dockerfilePath": "Dockerfile.prod"}, "variables": {"PORT": "${{ PORT }}", "CORE_API_URL": "https://mandy-api.railway.app", "SECRET_KEY": "${{ SECRET_KEY }}", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO"}, "domains": [{"name": "mandy.railway.app"}]}, {"name": "mandy-content-fetcher", "source": {"repo": "your-repo/mandy", "rootDirectory": "/workers"}, "build": {"dockerfilePath": "Dockerfile.prod", "dockerCommand": "python -m content_fetcher.main"}, "variables": {"GEMINI_API_KEY": "${{ GEMINI_API_KEY }}", "NEO4J_URI": "${{ NEO4J_URI }}", "NEO4J_USER": "${{ NEO4J_USER }}", "NEO4J_PASSWORD": "${{ NEO4J_PASSWORD }}", "CORE_API_URL": "https://mandy-api.railway.app", "NEWS_API_KEY": "${{ NEWS_API_KEY }}", "REDDIT_CLIENT_ID": "${{ REDDIT_CLIENT_ID }}", "REDDIT_CLIENT_SECRET": "${{ REDDIT_CLIENT_SECRET }}", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO"}}, {"name": "mandy-reflection-engine", "source": {"repo": "your-repo/mandy", "rootDirectory": "/workers"}, "build": {"dockerfilePath": "Dockerfile.prod", "dockerCommand": "python -m reflection_engine.main"}, "variables": {"GEMINI_API_KEY": "${{ GEMINI_API_KEY }}", "NEO4J_URI": "${{ NEO4J_URI }}", "NEO4J_USER": "${{ NEO4J_USER }}", "NEO4J_PASSWORD": "${{ NEO4J_PASSWORD }}", "CORE_API_URL": "https://mandy-api.railway.app", "ENVIRONMENT": "production", "LOG_LEVEL": "INFO"}}], "plugins": [{"name": "neo4j", "plan": "hobby", "variables": {"NEO4J_URI": "${{ PLUGIN_NEO4J_URL }}", "NEO4J_USER": "${{ PLUGIN_NEO4J_USER }}", "NEO4J_PASSWORD": "${{ PLUGIN_NEO4J_PASSWORD }}"}}]}