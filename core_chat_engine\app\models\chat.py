"""
Chat-related Pydantic models for the Mandy Core Chat Engine API.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field

from .common import BaseResponse


class ChatRequest(BaseModel):
    """Request model for chat interactions."""

    message: str = Field(..., description="User's message to <PERSON>", min_length=1, max_length=2000)
    user_id: str = Field(..., description="Unique identifier for the user")
    conversation_id: Optional[str] = Field(None, description="Conversation identifier for context")
    use_grounding: bool = Field(False, description="Whether to use Google Search Grounding for factual queries")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context for the conversation")


class MessageBurst(BaseModel):
    """A burst of text that simulates human-like typing."""

    text: str = Field(..., description="Text content of the burst")
    delay_before: float = Field(..., description="Delay in seconds before showing this burst")
    typing_duration: float = Field(..., description="Duration in seconds to show typing indicator")


class TypingIndicator(BaseModel):
    """Typing indicator for real-time chat experience."""

    is_typing: bool = Field(..., description="Whether Mandy is currently typing")
    estimated_duration: Optional[float] = Field(None, description="Estimated typing duration in seconds")


class GroundingMetadata(BaseModel):
    """Metadata for grounded responses using Google Search."""

    search_queries: List[str] = Field(default_factory=list, description="Search queries used")
    sources: List[Dict[str, Any]] = Field(default_factory=list, description="Source information")
    confidence_score: Optional[float] = Field(None, description="Confidence in the grounded information")


class ChatResponse(BaseResponse):
    """Response model for chat interactions."""

    response_text: str = Field(..., description="Mandy's response text")
    message_bursts: List[MessageBurst] = Field(default_factory=list, description="Text bursts for human-like delivery")
    conversation_id: str = Field(..., description="Conversation identifier")
    response_type: str = Field("emotional", description="Type of response: 'emotional' or 'factual'")
    grounding_metadata: Optional[GroundingMetadata] = Field(None, description="Grounding information if applicable")
    emotion_detected: Optional[str] = Field(None, description="Detected emotion in user's message")
    trust_level: Optional[float] = Field(None, description="Current trust level with the user (0.0-1.0)")
    memory_context_used: bool = Field(False, description="Whether memory context was used in the response")
    processing_time_ms: Optional[float] = Field(None, description="Time taken to process the request")


class ConversationSummary(BaseModel):
    """Summary of a conversation for history purposes."""

    conversation_id: str = Field(..., description="Unique conversation identifier")
    user_id: str = Field(..., description="User identifier")
    start_time: datetime = Field(..., description="When the conversation started")
    last_message_time: datetime = Field(..., description="When the last message was sent")
    message_count: int = Field(..., description="Total number of messages in the conversation")
    summary: str = Field(..., description="Brief summary of the conversation")
    trust_level: float = Field(..., description="Final trust level for this conversation")


class MessageHistory(BaseModel):
    """Individual message in conversation history."""

    message_id: str = Field(..., description="Unique message identifier")
    conversation_id: str = Field(..., description="Conversation identifier")
    sender: str = Field(..., description="Message sender: 'user' or 'mandy'")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(..., description="When the message was sent")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional message metadata")
