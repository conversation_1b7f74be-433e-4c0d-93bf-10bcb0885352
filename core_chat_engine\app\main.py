"""
Mandy Core Chat Engine - Main FastAPI Application

This is the core conversational AI service for <PERSON>, an emotionally intelligent
AI companion. It orchestrates the cognitive loop between Gemini AI and Graphiti
memory framework.
"""

import os
import time
import uuid
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import Fast<PERSON><PERSON>, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from loguru import logger
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import routes
from app.routes import auth, chat, health
from app.services.graphiti_service import GraphitiService


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Manage application lifecycle events.
    """
    # Startup: Initialize services
    logger.info("Starting Mandy Core Chat Engine...")

    # Initialize Graphiti service
    try:
        graphiti_service = GraphitiService()
        await graphiti_service.initialize()
        app.state.graphiti_service = graphiti_service
        logger.info("Graphiti service initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize Graphiti service: {e}")
        raise

    logger.info("Mandy Core Chat Engine started successfully")
    yield

    # Shutdown: Clean up resources
    logger.info("Shutting down Mandy Core Chat Engine...")


# Create FastAPI application
app = FastAPI(
    title="Mandy Core Chat Engine",
    description="Core conversational AI service for Mandy, an emotionally intelligent AI companion",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("DEBUG", "false").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "false").lower() == "true" else None,
)


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Request middleware for logging and timing
@app.middleware("http")
async def request_middleware(request: Request, call_next):
    """
    Process each request for logging, timing, and error handling.
    """
    # Generate request ID
    request_id = str(uuid.uuid4())
    request.state.request_id = request_id

    # Start timer
    start_time = time.time()

    try:
        # Process the request
        response = await call_next(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log successful request
        logger.info(
            f"Request completed",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "duration_ms": round(duration_ms, 2),
            }
        )

        # Add request ID to response headers
        response.headers["X-Request-ID"] = request_id
        return response

    except Exception as e:
        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log failed request
        logger.error(
            f"Request failed: {str(e)}",
            extra={
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "duration_ms": round(duration_ms, 2),
                "error": str(e),
            }
        )

        # Return error response
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "message": "Internal server error",
                "request_id": request_id,
            },
            headers={"X-Request-ID": request_id}
        )


# Include routers
app.include_router(health.router, tags=["Health"])
app.include_router(auth.router, prefix="/auth", tags=["Authentication"])
app.include_router(chat.router, prefix="/chat", tags=["Chat"])


# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint that returns API information."""
    return {
        "name": "Mandy Core Chat Engine",
        "version": "1.0.0",
        "status": "online",
        "description": "Emotionally intelligent AI companion core service"
    }


# Handle 404 errors
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc):
    """Handle 404 errors with a custom response."""
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "The requested resource was not found",
            "path": request.url.path,
            "request_id": getattr(request.state, "request_id", None),
        },
    )


# Handle validation errors
@app.exception_handler(422)
async def validation_exception_handler(request: Request, exc):
    """Handle validation errors with a custom response."""
    from fastapi.exceptions import RequestValidationError

    errors = []
    if hasattr(exc, 'errors'):
        for error in exc.errors():
            errors.append({
                "location": error.get("loc", []),
                "message": error.get("msg", ""),
                "type": error.get("type", ""),
            })

    return JSONResponse(
        status_code=422,
        content={
            "success": False,
            "message": "Validation error",
            "errors": errors,
            "request_id": getattr(request.state, "request_id", None),
        },
    )


if __name__ == "__main__":
    import uvicorn

    # Get configuration from environment
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    workers = int(os.getenv("API_WORKERS", "1"))
    debug = os.getenv("DEBUG", "false").lower() == "true"

    # Run the application
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=workers if not debug else 1,
        reload=debug,
        log_level="debug" if debug else "info",
    )
