"""
Centralized logging configuration for Mandy AI Companion.

This module provides structured logging with correlation IDs, performance metrics,
and security event tracking across all services.
"""

import json
import logging
import os
import sys
import time
from contextvars import ContextVar
from datetime import datetime
from typing import Any, Dict, Optional
from uuid import uuid4

from loguru import logger


# Context variables for request tracking
request_id_var: ContextVar[str] = ContextVar('request_id', default='')
user_id_var: ContextVar[str] = ContextVar('user_id', default='')
service_name_var: ContextVar[str] = ContextVar('service_name', default='')


class StructuredFormatter:
    """Custom formatter for structured JSON logging."""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
    
    def format(self, record):
        """Format log record as structured JSON."""
        log_entry = {
            "timestamp": datetime.utcnow().isoformat(),
            "service": self.service_name,
            "level": record["level"].name,
            "message": record["message"],
            "module": record["name"],
            "function": record["function"],
            "line": record["line"],
        }
        
        # Add context variables if available
        request_id = request_id_var.get('')
        if request_id:
            log_entry["request_id"] = request_id
        
        user_id = user_id_var.get('')
        if user_id:
            log_entry["user_id"] = user_id
        
        # Add extra fields from record
        if record["extra"]:
            log_entry.update(record["extra"])
        
        # Add exception info if present
        if record["exception"]:
            log_entry["exception"] = {
                "type": record["exception"].type.__name__,
                "message": str(record["exception"].value),
                "traceback": record["exception"].traceback
            }
        
        return json.dumps(log_entry, default=str)


def setup_logging(service_name: str, log_level: str = None) -> None:
    """
    Set up structured logging for a service.
    
    Args:
        service_name: Name of the service for log identification
        log_level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Set service name in context
    service_name_var.set(service_name)
    
    # Get configuration from environment
    log_level = log_level or os.getenv("LOG_LEVEL", "INFO")
    log_format = os.getenv("LOG_FORMAT", "json")
    
    # Remove default logger
    logger.remove()
    
    if log_format.lower() == "json":
        # Structured JSON logging
        formatter = StructuredFormatter(service_name)
        logger.add(
            sys.stdout,
            format=formatter.format,
            level=log_level,
            serialize=False
        )
    else:
        # Human-readable logging for development
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level=log_level
        )
    
    # Add file logging if configured
    log_file = os.getenv("LOG_FILE")
    if log_file:
        logger.add(
            log_file,
            format=formatter.format if log_format.lower() == "json" else None,
            level=log_level,
            rotation="100 MB",
            retention="30 days",
            compression="gz"
        )


def get_logger(name: str = None) -> Any:
    """Get a logger instance with the given name."""
    if name:
        return logger.bind(logger_name=name)
    return logger


def set_request_context(request_id: str = None, user_id: str = None) -> str:
    """
    Set request context for logging.
    
    Args:
        request_id: Unique request identifier
        user_id: User identifier for the request
    
    Returns:
        The request ID (generated if not provided)
    """
    if not request_id:
        request_id = str(uuid4())
    
    request_id_var.set(request_id)
    if user_id:
        user_id_var.set(user_id)
    
    return request_id


def clear_request_context():
    """Clear request context variables."""
    request_id_var.set('')
    user_id_var.set('')


class RequestLogger:
    """Context manager for request logging with timing."""
    
    def __init__(self, operation: str, **kwargs):
        self.operation = operation
        self.extra_fields = kwargs
        self.start_time = None
        self.logger = get_logger("request")
    
    def __enter__(self):
        self.start_time = time.time()
        self.logger.info(
            f"Starting {self.operation}",
            operation=self.operation,
            **self.extra_fields
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation}",
                operation=self.operation,
                duration_ms=round(duration * 1000, 2),
                status="success",
                **self.extra_fields
            )
        else:
            self.logger.error(
                f"Failed {self.operation}",
                operation=self.operation,
                duration_ms=round(duration * 1000, 2),
                status="error",
                error_type=exc_type.__name__,
                error_message=str(exc_val),
                **self.extra_fields
            )


def log_security_event(event_type: str, details: Dict[str, Any], severity: str = "INFO"):
    """
    Log security-related events.
    
    Args:
        event_type: Type of security event (login, logout, access_denied, etc.)
        details: Additional details about the event
        severity: Severity level (INFO, WARNING, ERROR, CRITICAL)
    """
    security_logger = get_logger("security")
    
    log_data = {
        "event_type": event_type,
        "security_event": True,
        **details
    }
    
    if severity.upper() == "CRITICAL":
        security_logger.critical("Security event", **log_data)
    elif severity.upper() == "ERROR":
        security_logger.error("Security event", **log_data)
    elif severity.upper() == "WARNING":
        security_logger.warning("Security event", **log_data)
    else:
        security_logger.info("Security event", **log_data)


def log_performance_metric(metric_name: str, value: float, unit: str = "ms", **tags):
    """
    Log performance metrics.
    
    Args:
        metric_name: Name of the metric
        value: Metric value
        unit: Unit of measurement
        **tags: Additional tags for the metric
    """
    perf_logger = get_logger("performance")
    
    perf_logger.info(
        f"Performance metric: {metric_name}",
        metric_name=metric_name,
        metric_value=value,
        metric_unit=unit,
        performance_metric=True,
        **tags
    )


class PerformanceTimer:
    """Context manager for timing operations and logging performance metrics."""
    
    def __init__(self, metric_name: str, **tags):
        self.metric_name = metric_name
        self.tags = tags
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (time.time() - self.start_time) * 1000  # Convert to milliseconds
        log_performance_metric(self.metric_name, duration, "ms", **self.tags)


# Convenience functions for common logging patterns
def log_api_request(method: str, path: str, status_code: int, duration_ms: float, **kwargs):
    """Log API request details."""
    api_logger = get_logger("api")
    
    api_logger.info(
        f"{method} {path} - {status_code}",
        http_method=method,
        http_path=path,
        http_status=status_code,
        duration_ms=duration_ms,
        api_request=True,
        **kwargs
    )


def log_database_operation(operation: str, table: str, duration_ms: float, **kwargs):
    """Log database operation details."""
    db_logger = get_logger("database")
    
    db_logger.info(
        f"Database {operation} on {table}",
        db_operation=operation,
        db_table=table,
        duration_ms=duration_ms,
        database_operation=True,
        **kwargs
    )


def log_external_api_call(service: str, endpoint: str, status_code: int, duration_ms: float, **kwargs):
    """Log external API call details."""
    external_logger = get_logger("external_api")
    
    external_logger.info(
        f"External API call to {service}",
        external_service=service,
        external_endpoint=endpoint,
        external_status=status_code,
        duration_ms=duration_ms,
        external_api_call=True,
        **kwargs
    )
