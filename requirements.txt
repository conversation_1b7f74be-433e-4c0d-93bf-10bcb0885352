# Core dependencies for Mandy AI Companion
# Production-grade, microservice-driven, emotionally intelligent AI companion

# ============================================================================
# CORE FRAMEWORK DEPENDENCIES
# ============================================================================

# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# Async HTTP client for inter-service communication
httpx==0.25.2
aiohttp==3.9.1

# ============================================================================
# AI & ML DEPENDENCIES
# ============================================================================

# Google Gemini API
google-generativeai==0.3.2

# Graphiti framework for temporal knowledge graphs
graphiti-core==0.17.6

# Neo4j driver for graph database
neo4j==5.15.0

# ============================================================================
# AUTHENTICATION & SECURITY
# ============================================================================

# JWT token handling
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Cryptography for secure operations
cryptography>=42.0.0

# ============================================================================
# WEB UI & INTERFACE
# ============================================================================

# Gradio for web interface
gradio==4.8.0

# ============================================================================
# BACKGROUND PROCESSING
# ============================================================================

# Scheduler for background tasks
APScheduler==3.10.4

# Redis for task queuing (optional)
redis==5.0.1
celery==5.3.4

# ============================================================================
# EXTERNAL API INTEGRATIONS
# ============================================================================

# News API client
newsapi-python==0.2.7

# Reddit API client
praw==7.7.1

# RSS feed parsing
feedparser==6.0.10

# Web scraping utilities
beautifulsoup4==4.12.2
requests==2.31.0

# ============================================================================
# LOGGING & MONITORING
# ============================================================================

# Structured logging
loguru==0.7.2

# Performance monitoring
psutil==5.9.6

# ============================================================================
# DATA PROCESSING & UTILITIES
# ============================================================================

# Date/time handling
python-dateutil==2.8.2

# JSON handling
orjson==3.9.10

# Environment variable loading
python-dotenv==1.0.0

# UUID utilities
uuid==1.30

# ============================================================================
# DEVELOPMENT & TESTING
# ============================================================================

# Testing framework
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0

# HTTP testing
httpx[test]==0.25.2

# Linting and formatting
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Type hints
types-requests==2.31.0.10
types-redis==4.6.0.11

# ============================================================================
# DEPLOYMENT & CONTAINERIZATION
# ============================================================================

# WSGI/ASGI servers
gunicorn==21.2.0

# Health checks
healthcheck==1.3.3

# ============================================================================
# OPTIONAL DEPENDENCIES
# ============================================================================

# Voice synthesis (experimental)
# pyttsx3==2.90

# Image processing (experimental)
# Pillow==10.1.0
# opencv-python==4.8.1.78

# Natural language processing
# spacy==3.7.2
# transformers==4.35.2

# ============================================================================
# PLATFORM-SPECIFIC DEPENDENCIES
# ============================================================================

# For Windows compatibility
# pywin32==306; sys_platform == "win32"

# For Linux/Unix systems
# python-magic==0.4.27; sys_platform != "win32"
