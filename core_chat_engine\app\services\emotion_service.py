"""
Emotion analysis service for detecting and analyzing user emotions.

This service uses Gemini to analyze emotional content in user messages
and provides emotional intelligence capabilities for <PERSON>.
"""

import asyncio
import json
import re
from typing import Dict, List, Optional
from uuid import UUID

from shared.models.base import Emotion, EmotionType
from shared.utils.config import get_config
from shared.utils.logging import get_logger, PerformanceTimer

from app.services.gemini_service import GeminiService, get_gemini_service


# Configuration and logger
config = get_config()
logger = get_logger("emotion_service")


class EmotionService:
    """Service for analyzing emotions in text using AI."""
    
    def __init__(self, gemini_service: Optional[GeminiService] = None):
        """
        Initialize emotion service.
        
        Args:
            gemini_service: Optional Gemini service instance
        """
        self.gemini_service = gemini_service or get_gemini_service()
        
        # Emotion keywords for fallback analysis
        self.emotion_keywords = {
            EmotionType.JOY: [
                "happy", "joy", "excited", "thrilled", "delighted", "cheerful",
                "glad", "pleased", "content", "elated", "euphoric", "blissful",
                "amazing", "wonderful", "fantastic", "great", "awesome", "love"
            ],
            EmotionType.SADNESS: [
                "sad", "depressed", "down", "blue", "melancholy", "gloomy",
                "dejected", "despondent", "heartbroken", "grief", "sorrow",
                "disappointed", "upset", "hurt", "crying", "tears"
            ],
            EmotionType.ANGER: [
                "angry", "mad", "furious", "rage", "irritated", "annoyed",
                "frustrated", "outraged", "livid", "irate", "pissed",
                "hate", "disgusted", "resentful", "bitter"
            ],
            EmotionType.FEAR: [
                "afraid", "scared", "terrified", "frightened", "anxious",
                "worried", "nervous", "panic", "dread", "phobia",
                "concerned", "uneasy", "apprehensive", "alarmed"
            ],
            EmotionType.SURPRISE: [
                "surprised", "shocked", "amazed", "astonished", "stunned",
                "bewildered", "confused", "unexpected", "sudden", "wow"
            ],
            EmotionType.DISGUST: [
                "disgusted", "revolted", "repulsed", "sickened", "nauseated",
                "appalled", "horrified", "gross", "yuck", "ew"
            ],
            EmotionType.TRUST: [
                "trust", "confident", "secure", "safe", "reliable",
                "dependable", "faithful", "loyal", "honest", "believe"
            ],
            EmotionType.ANTICIPATION: [
                "excited", "eager", "anticipating", "looking forward",
                "hopeful", "optimistic", "expecting", "awaiting", "can't wait"
            ]
        }
        
        logger.info("Emotion service initialized")
    
    async def analyze_emotions(self, text: str) -> List[Emotion]:
        """
        Analyze emotions in the given text.
        
        Args:
            text: Text to analyze for emotions
        
        Returns:
            List of detected emotions with confidence scores
        """
        if not text or not text.strip():
            return [Emotion(type=EmotionType.NEUTRAL, intensity=1.0, confidence=1.0)]
        
        with PerformanceTimer("emotion_analysis"):
            try:
                # Try AI-based emotion analysis first
                ai_emotions = await self._analyze_with_ai(text)
                
                if ai_emotions:
                    logger.info(
                        "AI emotion analysis completed",
                        text_length=len(text),
                        emotions_detected=len(ai_emotions)
                    )
                    return ai_emotions
                
                # Fallback to keyword-based analysis
                keyword_emotions = self._analyze_with_keywords(text)
                
                logger.info(
                    "Keyword emotion analysis completed",
                    text_length=len(text),
                    emotions_detected=len(keyword_emotions)
                )
                
                return keyword_emotions
                
            except Exception as e:
                logger.error(f"Emotion analysis failed: {str(e)}", exc_info=True)
                return [Emotion(type=EmotionType.NEUTRAL, intensity=0.5, confidence=0.3)]
    
    async def _analyze_with_ai(self, text: str) -> Optional[List[Emotion]]:
        """
        Analyze emotions using AI (Gemini).
        
        Args:
            text: Text to analyze
        
        Returns:
            List of emotions or None if analysis fails
        """
        try:
            prompt = f"""
            Analyze the emotional content of the following text and identify the emotions present.
            
            For each emotion detected, provide:
            - type: one of [joy, sadness, anger, fear, surprise, disgust, trust, anticipation, neutral]
            - intensity: how strong the emotion is (0.0 to 1.0)
            - confidence: how confident you are in this detection (0.0 to 1.0)
            
            Return a JSON array of emotion objects. If no clear emotions are detected, return neutral.
            
            Text: "{text}"
            
            Return only the JSON array, no other text.
            """
            
            response = await self.gemini_service.generate_response(
                context={"conversation": {"current_message": prompt}},
                user_id=UUID("00000000-0000-0000-0000-000000000000"),  # System user
                use_small_model=True
            )
            
            # Parse JSON response
            emotion_data = json.loads(response.strip())
            
            # Convert to Emotion objects
            emotions = []
            for item in emotion_data:
                try:
                    emotion_type = EmotionType(item["type"].lower())
                    emotion = Emotion(
                        type=emotion_type,
                        intensity=float(item["intensity"]),
                        confidence=float(item["confidence"])
                    )
                    emotions.append(emotion)
                except (ValueError, KeyError) as e:
                    logger.warning(f"Invalid emotion data: {item}, error: {str(e)}")
                    continue
            
            # Sort by intensity (strongest first)
            emotions.sort(key=lambda x: x.intensity, reverse=True)
            
            return emotions if emotions else None
            
        except Exception as e:
            logger.warning(f"AI emotion analysis failed: {str(e)}")
            return None
    
    def _analyze_with_keywords(self, text: str) -> List[Emotion]:
        """
        Analyze emotions using keyword matching (fallback method).
        
        Args:
            text: Text to analyze
        
        Returns:
            List of detected emotions
        """
        text_lower = text.lower()
        detected_emotions = {}
        
        # Count keyword matches for each emotion
        for emotion_type, keywords in self.emotion_keywords.items():
            matches = 0
            total_intensity = 0.0
            
            for keyword in keywords:
                # Use word boundaries to avoid partial matches
                pattern = r'\b' + re.escape(keyword) + r'\b'
                keyword_matches = len(re.findall(pattern, text_lower))
                
                if keyword_matches > 0:
                    matches += keyword_matches
                    # Weight intensity based on keyword strength and frequency
                    keyword_intensity = min(1.0, keyword_matches * 0.3)
                    total_intensity += keyword_intensity
            
            if matches > 0:
                # Calculate average intensity and confidence
                intensity = min(1.0, total_intensity / len(keywords))
                confidence = min(1.0, matches * 0.2)
                
                detected_emotions[emotion_type] = Emotion(
                    type=emotion_type,
                    intensity=intensity,
                    confidence=confidence
                )
        
        # Convert to list and sort by intensity
        emotions = list(detected_emotions.values())
        emotions.sort(key=lambda x: x.intensity, reverse=True)
        
        # If no emotions detected, return neutral
        if not emotions:
            emotions = [Emotion(type=EmotionType.NEUTRAL, intensity=1.0, confidence=0.8)]
        
        return emotions
    
    async def get_emotional_summary(self, emotions: List[Emotion]) -> Dict[str, any]:
        """
        Get a summary of emotional state from a list of emotions.
        
        Args:
            emotions: List of emotions to summarize
        
        Returns:
            Dictionary with emotional summary
        """
        if not emotions:
            return {
                "primary_emotion": "neutral",
                "emotional_intensity": 0.0,
                "emotional_complexity": 0.0,
                "dominant_emotions": [],
                "emotional_state": "neutral"
            }
        
        # Primary emotion (strongest)
        primary_emotion = emotions[0]
        
        # Calculate overall emotional intensity
        total_intensity = sum(emotion.intensity for emotion in emotions)
        avg_intensity = total_intensity / len(emotions)
        
        # Calculate emotional complexity (number of significant emotions)
        significant_emotions = [e for e in emotions if e.intensity > 0.3]
        emotional_complexity = len(significant_emotions) / 8.0  # Normalize by max emotions
        
        # Get dominant emotions (top 3)
        dominant_emotions = [
            {
                "type": emotion.type.value,
                "intensity": emotion.intensity,
                "confidence": emotion.confidence
            }
            for emotion in emotions[:3]
        ]
        
        # Determine overall emotional state
        if primary_emotion.intensity > 0.7:
            emotional_state = f"strongly_{primary_emotion.type.value}"
        elif primary_emotion.intensity > 0.4:
            emotional_state = f"moderately_{primary_emotion.type.value}"
        else:
            emotional_state = "neutral"
        
        return {
            "primary_emotion": primary_emotion.type.value,
            "emotional_intensity": avg_intensity,
            "emotional_complexity": emotional_complexity,
            "dominant_emotions": dominant_emotions,
            "emotional_state": emotional_state
        }
    
    def get_emotional_response_guidance(self, emotions: List[Emotion]) -> Dict[str, str]:
        """
        Get guidance for how to respond to detected emotions.
        
        Args:
            emotions: List of detected emotions
        
        Returns:
            Dictionary with response guidance
        """
        if not emotions:
            return {"tone": "neutral", "approach": "conversational"}
        
        primary_emotion = emotions[0]
        
        guidance_map = {
            EmotionType.JOY: {
                "tone": "enthusiastic",
                "approach": "Share in their happiness, ask about what's making them happy"
            },
            EmotionType.SADNESS: {
                "tone": "empathetic",
                "approach": "Offer comfort and support, listen actively, validate their feelings"
            },
            EmotionType.ANGER: {
                "tone": "calm",
                "approach": "Acknowledge their frustration, help them process, avoid escalation"
            },
            EmotionType.FEAR: {
                "tone": "reassuring",
                "approach": "Provide comfort and security, help them feel safe"
            },
            EmotionType.SURPRISE: {
                "tone": "curious",
                "approach": "Show interest in what surprised them, explore the situation"
            },
            EmotionType.DISGUST: {
                "tone": "understanding",
                "approach": "Acknowledge their feelings, help them process the situation"
            },
            EmotionType.TRUST: {
                "tone": "warm",
                "approach": "Build on the trust, be reliable and consistent"
            },
            EmotionType.ANTICIPATION: {
                "tone": "excited",
                "approach": "Share in their anticipation, ask about their expectations"
            },
            EmotionType.NEUTRAL: {
                "tone": "conversational",
                "approach": "Engage naturally, look for opportunities to connect"
            }
        }
        
        return guidance_map.get(primary_emotion.type, guidance_map[EmotionType.NEUTRAL])


# Dependency injection
_emotion_service: Optional[EmotionService] = None


def get_emotion_service() -> EmotionService:
    """Get the emotion service instance (singleton)."""
    global _emotion_service
    if _emotion_service is None:
        _emotion_service = EmotionService()
    return _emotion_service
