"""
User service for authentication and user management.

This service handles user creation, authentication, and user data management
for the Mandy AI companion system.
"""

from datetime import datetime
from typing import Dict, List, Optional
from uuid import UUID, uuid4

from shared.auth.jwt_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from shared.models.base import User, UserRole
from shared.utils.config import get_config
from shared.utils.logging import get_logger, log_security_event


# Configuration and logger
config = get_config()
logger = get_logger("user_service")


class UserService:
    """Service for user management and authentication."""
    
    def __init__(self):
        """Initialize user service."""
        self.jwt_handler = JWTHandler()
        
        # In-memory user storage for demo purposes
        # In production, this would be replaced with a proper database
        self._users: Dict[UUID, User] = {}
        self._users_by_username: Dict[str, UUID] = {}
        self._user_passwords: Dict[UUID, str] = {}
        
        # Create default admin user
        self._create_default_admin()
        
        logger.info("User service initialized")
    
    def _create_default_admin(self):
        """Create default admin user for initial access."""
        admin_username = config.service.environment == "development" and "admin" or "admin"
        admin_password = config.service.environment == "development" and "admin123" or "change_me_in_production"
        
        admin_user = User(
            id=uuid4(),
            username=admin_username,
            email="<EMAIL>",
            role=UserRole.ADMIN,
            group_id=f"admin_{uuid4().hex[:8]}",
            trust_score=1.0,
            is_active=True
        )
        
        # Hash password
        hashed_password = self.jwt_handler.hash_password(admin_password)
        
        # Store user
        self._users[admin_user.id] = admin_user
        self._users_by_username[admin_user.username] = admin_user.id
        self._user_passwords[admin_user.id] = hashed_password
        
        logger.info(
            "Created default admin user",
            username=admin_username,
            user_id=str(admin_user.id)
        )
    
    async def create_user(self, user: User, password: str) -> User:
        """
        Create a new user account.
        
        Args:
            user: User object with basic information
            password: Plain text password
        
        Returns:
            Created user object
        
        Raises:
            ValueError: If username already exists
        """
        try:
            # Check if username already exists
            if user.username in self._users_by_username:
                raise ValueError(f"Username '{user.username}' already exists")
            
            # Generate ID if not provided
            if not user.id:
                user.id = uuid4()
            
            # Hash password
            hashed_password = self.jwt_handler.hash_password(password)
            
            # Set creation timestamp
            user.created_at = datetime.utcnow()
            user.updated_at = user.created_at
            
            # Store user
            self._users[user.id] = user
            self._users_by_username[user.username] = user.id
            self._user_passwords[user.id] = hashed_password
            
            logger.info(
                "Created new user",
                user_id=str(user.id),
                username=user.username,
                role=user.role.value
            )
            
            # Log security event
            log_security_event(
                event_type="user_created",
                details={
                    "user_id": str(user.id),
                    "username": user.username,
                    "role": user.role.value
                },
                severity="INFO"
            )
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to create user: {str(e)}")
            raise
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        Authenticate a user with username and password.
        
        Args:
            username: Username
            password: Plain text password
        
        Returns:
            User object if authentication successful, None otherwise
        """
        try:
            # Find user by username
            user_id = self._users_by_username.get(username)
            if not user_id:
                logger.warning(f"Authentication failed: user not found", username=username)
                return None
            
            user = self._users.get(user_id)
            if not user:
                logger.error(f"User data inconsistency", user_id=str(user_id))
                return None
            
            # Check if user is active
            if not user.is_active:
                logger.warning(f"Authentication failed: user inactive", username=username)
                return None
            
            # Verify password
            stored_password = self._user_passwords.get(user_id)
            if not stored_password:
                logger.error(f"Password data missing", user_id=str(user_id))
                return None
            
            if not self.jwt_handler.verify_password(password, stored_password):
                logger.warning(f"Authentication failed: invalid password", username=username)
                return None
            
            # Update last interaction
            user.last_interaction = datetime.utcnow()
            user.interaction_count += 1
            
            logger.info(
                "User authenticated successfully",
                user_id=str(user.id),
                username=username
            )
            
            return user
            
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return None
    
    async def get_user_by_id(self, user_id: UUID) -> Optional[User]:
        """
        Get user by ID.
        
        Args:
            user_id: User ID
        
        Returns:
            User object or None if not found
        """
        try:
            # Handle string UUID
            if isinstance(user_id, str):
                user_id = UUID(user_id)
            
            user = self._users.get(user_id)
            
            if user:
                logger.debug(f"Retrieved user by ID", user_id=str(user_id))
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to get user by ID: {str(e)}")
            return None
    
    async def get_user_by_username(self, username: str) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            username: Username
        
        Returns:
            User object or None if not found
        """
        try:
            user_id = self._users_by_username.get(username)
            if not user_id:
                return None
            
            return await self.get_user_by_id(user_id)
            
        except Exception as e:
            logger.error(f"Failed to get user by username: {str(e)}")
            return None
    
    async def update_user(self, user: User) -> User:
        """
        Update user information.
        
        Args:
            user: Updated user object
        
        Returns:
            Updated user object
        """
        try:
            if user.id not in self._users:
                raise ValueError(f"User {user.id} not found")
            
            # Update timestamp
            user.updated_at = datetime.utcnow()
            
            # Store updated user
            self._users[user.id] = user
            
            logger.info(
                "Updated user",
                user_id=str(user.id),
                username=user.username
            )
            
            return user
            
        except Exception as e:
            logger.error(f"Failed to update user: {str(e)}")
            raise
    
    async def update_last_interaction(self, user_id: UUID):
        """
        Update user's last interaction timestamp.
        
        Args:
            user_id: User ID
        """
        try:
            user = await self.get_user_by_id(user_id)
            if user:
                user.last_interaction = datetime.utcnow()
                user.interaction_count += 1
                await self.update_user(user)
            
        except Exception as e:
            logger.error(f"Failed to update last interaction: {str(e)}")
    
    async def change_password(self, user_id: UUID, old_password: str, new_password: str) -> bool:
        """
        Change user password.
        
        Args:
            user_id: User ID
            old_password: Current password
            new_password: New password
        
        Returns:
            True if password changed successfully, False otherwise
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            # Verify old password
            stored_password = self._user_passwords.get(user_id)
            if not stored_password or not self.jwt_handler.verify_password(old_password, stored_password):
                logger.warning(f"Password change failed: invalid old password", user_id=str(user_id))
                return False
            
            # Hash new password
            new_hashed_password = self.jwt_handler.hash_password(new_password)
            
            # Update password
            self._user_passwords[user_id] = new_hashed_password
            
            # Update user timestamp
            user.updated_at = datetime.utcnow()
            await self.update_user(user)
            
            logger.info(f"Password changed successfully", user_id=str(user_id))
            
            # Log security event
            log_security_event(
                event_type="password_changed",
                details={"user_id": str(user_id), "username": user.username},
                severity="INFO"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to change password: {str(e)}")
            return False
    
    async def deactivate_user(self, user_id: UUID) -> bool:
        """
        Deactivate a user account.
        
        Args:
            user_id: User ID
        
        Returns:
            True if deactivated successfully, False otherwise
        """
        try:
            user = await self.get_user_by_id(user_id)
            if not user:
                return False
            
            user.is_active = False
            user.updated_at = datetime.utcnow()
            
            await self.update_user(user)
            
            logger.info(f"User deactivated", user_id=str(user_id))
            
            # Log security event
            log_security_event(
                event_type="user_deactivated",
                details={"user_id": str(user_id), "username": user.username},
                severity="WARNING"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to deactivate user: {str(e)}")
            return False
    
    async def get_user_stats(self) -> Dict[str, int]:
        """
        Get user statistics.
        
        Returns:
            Dictionary with user statistics
        """
        try:
            total_users = len(self._users)
            active_users = sum(1 for user in self._users.values() if user.is_active)
            admin_users = sum(1 for user in self._users.values() if user.role == UserRole.ADMIN)
            
            return {
                "total_users": total_users,
                "active_users": active_users,
                "inactive_users": total_users - active_users,
                "admin_users": admin_users,
                "regular_users": total_users - admin_users
            }
            
        except Exception as e:
            logger.error(f"Failed to get user stats: {str(e)}")
            return {}


# Dependency injection
_user_service: Optional[UserService] = None


def get_user_service() -> UserService:
    """Get the user service instance (singleton)."""
    global _user_service
    if _user_service is None:
        _user_service = UserService()
    return _user_service
